{% extends "base.html" %}

{% block title %}Terms of Service | JWT Decode Online{% endblock %}

{% block description %}Terms and conditions for using JWT Decode Online's JWT token decoding, validation, and generation tools. Read our service agreement before using our services.{% endblock %}

{% block meta_keywords %}terms of service, jwt decode online terms, service agreement, jwt token decoder terms{% endblock %}

{% block content %}
<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item active" aria-current="page">Terms of Service</li>
    </ol>
</nav>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-4 mb-3">Terms of Service</h1>
                <p class="lead">Last updated: March 26, 2023</p>
            </div>
            
            <!-- Introduction -->
            <div class="card mb-4">
                <div class="card-body">
                    <h2 class="h4 mb-3">Introduction</h2>
                    <p>Welcome to JWT Decode Online. These Terms of Service ("Terms") govern your use of our website located at <a href="https://jwtdecode.online">jwtdecode.online</a> ("Service") operated by JWT Decode Online ("us", "we", or "our").</p>
                    <p>By accessing or using the Service, you agree to be bound by these Terms. If you disagree with any part of the terms, then you may not access the Service.</p>
                </div>
            </div>
            
            <!-- Use License -->
            <div class="card mb-4">
                <div class="card-body">
                    <h2 class="h4 mb-3">Use License</h2>
                    <p>Permission is granted to temporarily use the tools and services on JWT Decode Online's website for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:</p>
                    <ul>
                        <li>Modify or copy the materials;</li>
                        <li>Use the materials for any commercial purpose, or for any public display (commercial or non-commercial);</li>
                        <li>Attempt to decompile or reverse engineer any software contained on JWT Decode Online's website;</li>
                        <li>Remove any copyright or other proprietary notations from the materials; or</li>
                        <li>Transfer the materials to another person or "mirror" the materials on any other server.</li>
                    </ul>
                    <p>This license shall automatically terminate if you violate any of these restrictions and may be terminated by JWT Decode Online at any time. Upon terminating your viewing of these materials or upon the termination of this license, you must destroy any downloaded materials in your possession whether in electronic or printed format.</p>
                </div>
            </div>
            
            <!-- Disclaimer -->
            <div class="card mb-4">
                <div class="card-body">
                    <h2 class="h4 mb-3">Disclaimer</h2>
                    <p>The materials on JWT Decode Online's website are provided on an 'as is' basis. JWT Decode Online makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including, without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.</p>
                    <p>Further, JWT Decode Online does not warrant or make any representations concerning the accuracy, likely results, or reliability of the use of the materials on its website or otherwise relating to such materials or on any sites linked to this site.</p>
                    <p><strong>Security and JWT Tokens:</strong> While our JWT decoding and validation tools are designed to help with security-related tasks, you should not rely solely on our tools for implementing security in your applications. JWT Decode Online is not responsible for any security breaches or vulnerabilities in your applications.</p>
                </div>
            </div>
            
            <!-- Limitations -->
            <div class="card mb-4">
                <div class="card-body">
                    <h2 class="h4 mb-3">Limitations</h2>
                    <p>In no event shall JWT Decode Online or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on JWT Decode Online's website, even if JWT Decode Online or a JWT Decode Online authorized representative has been notified orally or in writing of the possibility of such damage.</p>
                    <p>Because some jurisdictions do not allow limitations on implied warranties, or limitations of liability for consequential or incidental damages, these limitations may not apply to you.</p>
                </div>
            </div>
            
            <!-- Accuracy of Materials -->
            <div class="card mb-4">
                <div class="card-body">
                    <h2 class="h4 mb-3">Accuracy of Materials</h2>
                    <p>The materials appearing on JWT Decode Online's website could include technical, typographical, or photographic errors. JWT Decode Online does not warrant that any of the materials on its website are accurate, complete or current. JWT Decode Online may make changes to the materials contained on its website at any time without notice. However, JWT Decode Online does not make any commitment to update the materials.</p>
                </div>
            </div>
            
            <!-- Links -->
            <div class="card mb-4">
                <div class="card-body">
                    <h2 class="h4 mb-3">Links</h2>
                    <p>JWT Decode Online has not reviewed all of the sites linked to its website and is not responsible for the contents of any such linked site. The inclusion of any link does not imply endorsement by JWT Decode Online of the site. Use of any such linked website is at the user's own risk.</p>
                </div>
            </div>
            
            <!-- Modifications -->
            <div class="card mb-4">
                <div class="card-body">
                    <h2 class="h4 mb-3">Modifications</h2>
                    <p>JWT Decode Online may revise these terms of service for its website at any time without notice. By using this website you are agreeing to be bound by the then current version of these terms of service.</p>
                </div>
            </div>
            
            <!-- Governing Law -->
            <div class="card mb-4">
                <div class="card-body">
                    <h2 class="h4 mb-3">Governing Law</h2>
                    <p>These terms and conditions are governed by and construed in accordance with the laws and you irrevocably submit to the exclusive jurisdiction of the courts in that location.</p>
                </div>
            </div>
            
            <!-- Contact Us -->
            <div class="card mb-4">
                <div class="card-body">
                    <h2 class="h4 mb-3">Contact Us</h2>
                    <p>If you have any questions about these Terms, please contact us:</p>
                    <ul>
                        <li>By email: <a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li>By visiting: <a href="https://jwtdecode.online/contact">https://jwtdecode.online/contact</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 1.5rem;
}

.card h2 {
    color: var(--bs-primary);
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.breadcrumb {
    background-color: transparent;
    padding: 0.75rem 0;
}
</style>
{% endblock %} 