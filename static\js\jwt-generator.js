/**
 * JWT Generator JavaScript
 * Client-side JWT token generation with multiple algorithm support
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements - Header
    const algorithmSelect = document.getElementById('algorithm');
    const tokenTypeSelect = document.getElementById('token-type');
    const addCustomHeaderCheckbox = document.getElementById('add-custom-header');
    const customHeaderSection = document.getElementById('custom-header-section');
    const customHeaderTextarea = document.getElementById('custom-header');
    
    // DOM Elements - Payload
    const issuerInput = document.getElementById('iss-claim');
    const subjectInput = document.getElementById('sub-claim');
    const audienceInput = document.getElementById('aud-claim');
    const expValueInput = document.getElementById('exp-value');
    const expUnitSelect = document.getElementById('exp-unit');
    const nbfValueInput = document.getElementById('nbf-value');
    const nbfUnitSelect = document.getElementById('nbf-unit');
    const jtiInput = document.getElementById('jti-claim');
    const generateJtiBtn = document.getElementById('generate-jti-btn');
    const addStandardClaimsBtn = document.getElementById('add-standard-claims-btn');
    const clearPayloadBtn = document.getElementById('clear-payload-btn');
    const customPayloadTextarea = document.getElementById('custom-payload');
    const payloadPreview = document.getElementById('payload-preview');
    
    // DOM Elements - Signature
    const secretKeyInput = document.getElementById('secret-key');
    const generateSecretBtn = document.getElementById('generate-secret-btn');
    const privateKeyTextarea = document.getElementById('private-key');
    const publicKeyTextarea = document.getElementById('public-key');
    const generateKeypairBtn = document.getElementById('generate-keypair-btn');
    const symmetricKeySection = document.getElementById('symmetric-key-section');
    const asymmetricKeySection = document.getElementById('asymmetric-key-section');
    
    // DOM Elements - Key Pair Modal
    const keyTypeSelect = document.getElementById('key-type');
    const rsaKeySize = document.getElementById('rsa-key-size');
    const ecCurve = document.getElementById('ec-curve');
    const rsaOptions = document.getElementById('rsa-options');
    const ecOptions = document.getElementById('ec-options');
    const confirmGenerateKeypairBtn = document.getElementById('confirm-generate-keypair');
    
    // DOM Elements - Generation & Results
    const generateTokenBtn = document.getElementById('generate-token-btn');
    const resultSection = document.getElementById('result-section');
    const headerPart = document.getElementById('header-part');
    const payloadPart = document.getElementById('payload-part');
    const signaturePart = document.getElementById('signature-part');
    const generatedTokenTextarea = document.getElementById('generated-token');
    const copyTokenBtn = document.getElementById('copy-token-btn');
    const downloadTokenBtn = document.getElementById('download-token-btn');
    const tokenAlgorithmInfo = document.getElementById('token-algorithm-info');
    const tokenIatInfo = document.getElementById('token-iat-info');
    const tokenExpInfo = document.getElementById('token-exp-info');
    const tokenSizeInfo = document.getElementById('token-size-info');
    
    // Library reference (jsrsasign)
    const KJUR = window.KJUR || {};
    
    // Syntax highlighting colors for JSON
    const syntaxColors = {
        key: '#e83e8c',
        string: '#28a745',
        number: '#007bff',
        boolean: '#ff6b6b',
        null: '#6c757d'
    };
    
    // Initialize event listeners
    function initEventListeners() {
        // Algorithm change handler
        algorithmSelect.addEventListener('change', handleAlgorithmChange);
        
        // Custom header toggle
        addCustomHeaderCheckbox.addEventListener('change', () => {
            customHeaderSection.style.display = addCustomHeaderCheckbox.checked ? 'block' : 'none';
            updatePayloadPreview();
        });
        
        // Generate JTI
        generateJtiBtn.addEventListener('click', () => {
            jtiInput.value = generateUUID();
            updatePayloadPreview();
        });
        
        // Add standard claims
        addStandardClaimsBtn.addEventListener('click', () => {
            if (!issuerInput.value) issuerInput.value = 'https://your-domain.com';
            if (!subjectInput.value) subjectInput.value = 'user_' + Math.floor(Math.random() * 1000);
            if (!audienceInput.value) audienceInput.value = 'your-client-id';
            if (!expValueInput.value) expValueInput.value = '1';
            if (!jtiInput.value) jtiInput.value = generateUUID();
            updatePayloadPreview();
        });
        
        // Clear payload
        clearPayloadBtn.addEventListener('click', () => {
            issuerInput.value = '';
            subjectInput.value = '';
            audienceInput.value = '';
            expValueInput.value = '1';
            nbfValueInput.value = '0';
            jtiInput.value = '';
            customPayloadTextarea.value = '';
            updatePayloadPreview();
        });
        
        // Generate secret key
        generateSecretBtn.addEventListener('click', () => {
            const secretLength = 64; // 512 bits
            secretKeyInput.value = generateRandomString(secretLength);
        });
        
        // Generate key pair modal toggle
        generateKeypairBtn.addEventListener('click', () => {
            const keypairModal = new bootstrap.Modal(document.getElementById('keypairModal'));
            keypairModal.show();
        });
        
        // Key type change
        keyTypeSelect.addEventListener('change', () => {
            const isRSA = keyTypeSelect.value === 'RSA';
            rsaOptions.style.display = isRSA ? 'block' : 'none';
            ecOptions.style.display = isRSA ? 'none' : 'block';
        });
        
        // Generate key pair
        confirmGenerateKeypairBtn.addEventListener('click', generateKeyPair);
        
        // Generate token
        generateTokenBtn.addEventListener('click', generateToken);
        
        // Copy token
        copyTokenBtn.addEventListener('click', () => {
            generatedTokenTextarea.select();
            document.execCommand('copy');
            showTooltip(copyTokenBtn, 'Copied!');
        });
        
        // Download token
        downloadTokenBtn.addEventListener('click', () => {
            const token = generatedTokenTextarea.value;
            if (!token) return;
            
            const blob = new Blob([token], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'jwt-token.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });
        
        // Real-time payload preview updates
        const payloadInputs = [
            issuerInput, subjectInput, audienceInput, 
            expValueInput, expUnitSelect, 
            nbfValueInput, nbfUnitSelect, 
            jtiInput, customPayloadTextarea
        ];
        
        payloadInputs.forEach(input => {
            input.addEventListener('input', updatePayloadPreview);
        });
        
        // Initialize with default values
        handleAlgorithmChange();
        updatePayloadPreview();
    }
    
    // Handle algorithm change
    function handleAlgorithmChange() {
        const algorithm = algorithmSelect.value;
        const isSymmetric = algorithm.startsWith('HS');
        
        symmetricKeySection.style.display = isSymmetric ? 'block' : 'none';
        asymmetricKeySection.style.display = isSymmetric ? 'none' : 'block';
    }
    
    // Update payload preview
    function updatePayloadPreview() {
        try {
            const payload = buildPayloadObject();
            payloadPreview.innerHTML = formatJSON(payload);
        } catch (error) {
            payloadPreview.innerHTML = `<span style="color: red;">Error: ${error.message}</span>`;
        }
    }
    
    // Build the payload object from form inputs
    function buildPayloadObject() {
        const now = Math.floor(Date.now() / 1000);
        const payload = {};
        
        // Add standard claims if provided
        if (issuerInput.value) payload.iss = issuerInput.value;
        if (subjectInput.value) payload.sub = subjectInput.value;
        if (audienceInput.value) payload.aud = audienceInput.value;
        
        // Always add issued at (iat) claim
        payload.iat = now;
        
        // Add expiration if provided
        if (expValueInput.value && parseInt(expValueInput.value) > 0) {
            const expValue = parseInt(expValueInput.value);
            let expSeconds = 0;
            
            switch (expUnitSelect.value) {
                case 'hours':
                    expSeconds = expValue * 60 * 60;
                    break;
                case 'days':
                    expSeconds = expValue * 24 * 60 * 60;
                    break;
                case 'months':
                    expSeconds = expValue * 30 * 24 * 60 * 60; // Approximation
                    break;
            }
            
            payload.exp = now + expSeconds;
        }
        
        // Add not before if provided
        if (nbfValueInput.value && parseInt(nbfValueInput.value) > 0) {
            const nbfValue = parseInt(nbfValueInput.value);
            let nbfSeconds = 0;
            
            switch (nbfUnitSelect.value) {
                case 'minutes':
                    nbfSeconds = nbfValue * 60;
                    break;
                case 'hours':
                    nbfSeconds = nbfValue * 60 * 60;
                    break;
                case 'days':
                    nbfSeconds = nbfValue * 24 * 60 * 60;
                    break;
            }
            
            payload.nbf = now + nbfSeconds;
        }
        
        // Add JWT ID if provided
        if (jtiInput.value) payload.jti = jtiInput.value;
        
        // Parse and merge custom claims if provided
        if (customPayloadTextarea.value.trim()) {
            try {
                const customClaims = JSON.parse(customPayloadTextarea.value);
                Object.assign(payload, customClaims);
            } catch (error) {
                throw new Error(`Invalid custom claims JSON: ${error.message}`);
            }
        }
        
        return payload;
    }
    
    // Parse header JSON input
    function parseCustomHeader() {
        if (!addCustomHeaderCheckbox.checked || !customHeaderTextarea.value.trim()) {
            return {};
        }
        
        try {
            return JSON.parse(customHeaderTextarea.value);
        } catch (error) {
            throw new Error(`Invalid custom header JSON: ${error.message}`);
        }
    }
    
    // Generate JWT token
    function generateToken() {
        try {
            const algorithm = algorithmSelect.value;
            const payload = buildPayloadObject();
            const isSymmetric = algorithm.startsWith('HS');
            
            // Create header
            const header = {
                alg: algorithm,
                typ: tokenTypeSelect.value
            };
            
            // Merge in any custom header fields
            const customHeader = parseCustomHeader();
            Object.assign(header, customHeader);
            
            // Prepare for signing
            let token;
            
            if (isSymmetric) {
                // HMAC signing (symmetric)
                if (!secretKeyInput.value) {
                    throw new Error('Secret key is required for HMAC algorithms');
                }
                
                token = KJUR.jws.JWS.sign(
                    null,           // unused
                    header,         // header
                    payload,        // payload
                    secretKeyInput.value // secret key (utf8)
                );
            } else {
                // RSA or ECDSA signing (asymmetric)
                if (!privateKeyTextarea.value) {
                    throw new Error('Private key is required for RSA/ECDSA algorithms');
                }
                
                token = KJUR.jws.JWS.sign(
                    null,           // unused
                    header,         // header
                    payload,        // payload
                    privateKeyTextarea.value // PEM private key
                );
            }
            
            // Display the token and results
            displayToken(token, header, payload);
            
        } catch (error) {
            showError('Error generating token: ' + error.message);
        }
    }
    
    // Display generated token
    function displayToken(token, header, payload) {
        // Show result section
        resultSection.style.display = 'block';
        
        // Scroll to results
        resultSection.scrollIntoView({ behavior: 'smooth' });
        
        // Parse token parts
        const parts = token.split('.');
        const encodedHeader = parts[0];
        const encodedPayload = parts[1];
        const signature = parts[2];
        
        // Display token parts in visualization
        headerPart.textContent = encodedHeader;
        payloadPart.textContent = encodedPayload;
        signaturePart.textContent = signature.length > 20 ? 
            signature.substring(0, 20) + '...' : 
            signature;
        
        // Set token in textarea
        generatedTokenTextarea.value = token;
        
        // Update token info
        tokenAlgorithmInfo.textContent = header.alg;
        
        // Format dates
        const iat = payload.iat;
        const exp = payload.exp;
        
        tokenIatInfo.textContent = iat ? 
            new Date(iat * 1000).toLocaleString() : 
            'Not specified';
            
        tokenExpInfo.textContent = exp ? 
            new Date(exp * 1000).toLocaleString() : 
            'No expiration';
            
        // Calculate token size
        const bytes = new Blob([token]).size;
        tokenSizeInfo.textContent = formatBytes(bytes);
        
        // Add animation to parts
        animateElement(headerPart.parentNode);
        setTimeout(() => animateElement(payloadPart.parentNode), 200);
        setTimeout(() => animateElement(signaturePart.parentNode), 400);
    }
    
    // Generate key pair
    function generateKeyPair() {
        try {
            const keyType = keyTypeSelect.value;
            const progressBar = document.querySelector('.progress');
            const progressBarInner = progressBar.querySelector('.progress-bar');
            
            // Show progress
            progressBar.style.display = 'block';
            progressBarInner.style.width = '0%';
            
            // Disable button during generation
            confirmGenerateKeypairBtn.disabled = true;
            confirmGenerateKeypairBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Generating...';
            
            // Use setTimeout to allow UI to update
            setTimeout(() => {
                try {
                    let keyPair;
                    
                    // Update progress
                    progressBarInner.style.width = '30%';
                    
                    if (keyType === 'RSA') {
                        // Generate RSA key pair
                        const keySize = parseInt(rsaKeySize.value);
                        keyPair = KJUR.KEYUTIL.generateKeypair("RSA", keySize);
                    } else {
                        // Generate EC key pair
                        const curve = ecCurve.value;
                        keyPair = KJUR.KEYUTIL.generateKeypair("EC", curve);
                    }
                    
                    // Update progress
                    progressBarInner.style.width = '70%';
                    
                    // Get PEM format keys
                    const prvKey = KJUR.KEYUTIL.getPEM(keyPair.prvKeyObj, "PKCS8PRV");
                    const pubKey = KJUR.KEYUTIL.getPEM(keyPair.pubKeyObj, "PKCS8PUB");
                    
                    // Set in textareas
                    privateKeyTextarea.value = prvKey;
                    publicKeyTextarea.value = pubKey;
                    
                    // Complete progress and reset UI
                    progressBarInner.style.width = '100%';
                    setTimeout(() => {
                        progressBar.style.display = 'none';
                        confirmGenerateKeypairBtn.disabled = false;
                        confirmGenerateKeypairBtn.innerHTML = 'Generate';
                        
                        // Close modal
                        const keypairModal = bootstrap.Modal.getInstance(document.getElementById('keypairModal'));
                        keypairModal.hide();
                    }, 500);
                    
                } catch (error) {
                    showError('Error generating key pair: ' + error.message);
                    confirmGenerateKeypairBtn.disabled = false;
                    confirmGenerateKeypairBtn.innerHTML = 'Generate';
                    progressBar.style.display = 'none';
                }
            }, 100);
            
        } catch (error) {
            showError('Error in key pair generation: ' + error.message);
        }
    }
    
    // Format JSON with syntax highlighting
    function formatJSON(obj) {
        if (!obj) return '';
        
        let json = JSON.stringify(obj, null, 4);
        
        // Apply syntax highlighting
        json = json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
            let cls = syntaxColors.number; // number
            if (/^"/.test(match)) {
                if (/:$/.test(match)) {
                    cls = syntaxColors.key; // key
                } else {
                    cls = syntaxColors.string; // string
                }
            } else if (/true|false/.test(match)) {
                cls = syntaxColors.boolean; // boolean
            } else if (/null/.test(match)) {
                cls = syntaxColors.null; // null
            }
            return '<span style="color:' + cls + '">' + match + '</span>';
        });
        
        return json;
    }
    
    // Utility: Generate UUID v4
    function generateUUID() {
        // Implementation from RFC4122 version 4 UUID
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
    
    // Utility: Generate random string
    function generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~+/';
        let result = '';
        const randomValues = new Uint8Array(length);
        window.crypto.getRandomValues(randomValues);
        
        for (let i = 0; i < length; i++) {
            result += chars.charAt(randomValues[i] % chars.length);
        }
        
        return result;
    }
    
    // Utility: Format bytes to human readable size
    function formatBytes(bytes, decimals = 2) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }
    
    // Utility: Animate element with fade-in effect
    function animateElement(element) {
        element.style.transition = 'none';
        element.style.opacity = '0';
        element.style.transform = 'translateY(10px)';
        
        // Force reflow
        void element.offsetWidth;
        
        element.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
    }
    
    // Utility: Show error message
    function showError(message) {
        // Create error toast
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed bottom-0 end-0 m-3';
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-exclamation-circle me-2"></i> ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        // Add to document
        document.body.appendChild(toast);
        
        // Show toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 5000
        });
        
        bsToast.show();
        
        // Remove from DOM after hiding
        toast.addEventListener('hidden.bs.toast', function () {
            document.body.removeChild(toast);
        });
    }
    
    // Utility: Show tooltip
    function showTooltip(element, message) {
        // Original title
        const originalTitle = element.getAttribute('data-original-title') || element.getAttribute('title');
        
        // Store original title if not already stored
        if (!element.getAttribute('data-original-title')) {
            element.setAttribute('data-original-title', originalTitle || '');
        }
        
        // Set new title
        element.setAttribute('title', message);
        
        // Force title to update
        element.dispatchEvent(new MouseEvent('mouseover'));
        
        // Restore original title after delay
        setTimeout(() => {
            element.setAttribute('title', element.getAttribute('data-original-title') || '');
            element.dispatchEvent(new MouseEvent('mouseout'));
        }, 1000);
    }
    
    // Initialize when DOM is loaded
    initEventListeners();
}); 