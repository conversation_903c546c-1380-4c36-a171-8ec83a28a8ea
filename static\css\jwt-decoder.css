/* JWT Decoder CSS */

/* Global Colors */
:root {
    --primary-color: #4ade80;
    --secondary-color: #86efac;
    --accent-color: #059669;
    --header-color: #10b981;
    --payload-color: #6ee7b7;
    --signature-color: #34d399;
    --dark-color: #166534;
    --light-color: #f0fdf4;
    --shadow: 0 5px 20px rgba(16, 185, 129, 0.15);
    --hover-shadow: 0 10px 25px rgba(16, 185, 129, 0.2);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    padding: 4rem 0;
    color: #fff;
    border-radius: 0 0 30px 30px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(5, 150, 105, 0.25);
}

.hero-section::before {
    content: '';
    position: absolute;
    width: 150%;
    height: 100%;
    background: rgba(255, 255, 255, 0.08);
    transform: rotate(-45deg);
    top: -50%;
    left: -25%;
    pointer-events: none;
}

.hero-section::after {
    content: '';
    position: absolute;
    width: 150%;
    height: 100%;
    background: rgba(255, 255, 255, 0.05);
    transform: rotate(45deg);
    bottom: -50%;
    right: -25%;
    pointer-events: none;
}

/* Floating elements to create depth */
.hero-section .container::before {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
    border-radius: 50%;
    top: 10%;
    left: 5%;
    animation: float 15s infinite ease-in-out;
}

.hero-section .container::after {
    content: '';
    position: absolute;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
    border-radius: 50%;
    bottom: 10%;
    right: 5%;
    animation: float 20s infinite ease-in-out reverse;
}

@keyframes float {
    0%, 100% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(20px, 10px);
    }
    50% {
        transform: translate(0, 20px);
    }
    75% {
        transform: translate(-10px, 10px);
    }
}

.hero-title {
    font-weight: 700;
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.3px;
    line-height: 1.3;
    color: #ffffff;
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.95;
    margin-bottom: 2rem;
    font-weight: 400;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.hero-section .btn {
    padding: 1rem 2.5rem;
    font-size: 1.2rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
    transition: all 0.3s ease;
    border-radius: 30px;
    letter-spacing: 0.3px;
    border: none;
    background: #ffffff;
    color: var(--accent-color);
}

.hero-section .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(5, 150, 105, 0.4);
    background: #f8f9fa;
}

.text-highlight {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.25);
    padding: 0.1em 0.3em;
    border-radius: 8px;
    margin: 0 -0.1em;
    position: relative;
    display: inline-block;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.text-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.2) 100%);
    border-radius: 8px;
    z-index: -1;
    opacity: 0.7;
    animation: shimmer 2.5s infinite linear;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Main Decoder Section */
.decoder-section {
    min-height: calc(100vh - 80px);
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    padding: 2rem 0;
}

.main-decoder-card {
    background-color: #ffffff;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(74, 222, 128, 0.15);
    padding: 3rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(74, 222, 128, 0.1);
}

.decoder-header {
    margin-bottom: 2rem;
}

.decoder-main-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.decoder-subtitle {
    font-size: 1.1rem;
    color: var(--light-text);
    margin-bottom: 0;
}

/* Input Section Styles */
.decoder-input-section {
    margin-bottom: 3rem;
}

.input-wrapper {
    position: relative;
}

.input-label {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.jwt-input {
    width: 100%;
    min-height: 150px;
    padding: 1.5rem;
    font-family: 'Roboto Mono', monospace;
    font-size: 0.95rem;
    line-height: 1.5;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    background-color: #fafafa;
    transition: all 0.3s ease;
    resize: vertical;
}

.jwt-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: #ffffff;
    box-shadow: 0 0 0 4px rgba(74, 222, 128, 0.1);
}

.jwt-input::placeholder {
    color: #9ca3af;
    font-style: italic;
}

/* Input Actions */
.input-actions {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 1rem;
    justify-content: space-between;
}

.btn-decode {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border: none;
    padding: 0.8rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(74, 222, 128, 0.3);
}

.btn-decode:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 222, 128, 0.4);
    background: linear-gradient(135deg, var(--primary-hover), var(--accent-color));
}

.btn-clear {
    background: transparent;
    color: var(--light-text);
    border: 2px solid var(--border-color);
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.btn-clear:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(74, 222, 128, 0.05);
}

.input-info {
    flex: 1;
    text-align: right;
}

@media (max-width: 768px) {
    .input-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .input-info {
        text-align: center;
        order: -1;
        margin-bottom: 1rem;
    }
}

/* Results Section */
.decoder-results {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid var(--border-color);
    animation: fadeInUp 0.6s ease-out;
}

.results-header {
    text-align: center;
}

.results-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0;
}

.result-card {
    background: #ffffff;
    border: 2px solid var(--border-color);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.result-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(74, 222, 128, 0.15);
    transform: translateY(-2px);
}

.result-card-header {
    padding: 1.5rem 1.5rem 1rem;
    background: linear-gradient(135deg, #f8fffe 0%, #f0fdf4 100%);
    border-bottom: 1px solid var(--border-color);
}

.card-title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.card-description {
    font-size: 0.9rem;
    color: var(--light-text);
    margin: 0;
}

.claims-badge {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

/* Result Card Body and JSON Display */
.result-card-body {
    padding: 1.5rem;
}

.json-display {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.2rem;
    margin: 0;
    font-family: 'Roboto Mono', monospace;
    font-size: 0.85rem;
    line-height: 1.6;
    color: #2d3748;
    overflow-x: auto;
    max-height: 300px;
    overflow-y: auto;
}

.signature-display {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.2rem;
    font-family: 'Roboto Mono', monospace;
    font-size: 0.85rem;
    word-break: break-all;
    color: #2d3748;
}

.verification-status {
    padding: 0.8rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    text-align: center;
}

.copy-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--light-text);
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.copy-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(74, 222, 128, 0.05);
}

.copy-btn.copied {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Results Actions */
.results-actions {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.action-btn {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    color: var(--dark-color);
    border: 2px solid var(--border-color);
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 10px;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: linear-gradient(135deg, #ffffff, rgba(74, 222, 128, 0.05));
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 222, 128, 0.2);
    text-decoration: none;
}

/* Card Color Variations */
.header-card .result-card-header {
    background: linear-gradient(135deg, #fef3f2, #fef2f2);
}

.payload-card .result-card-header {
    background: linear-gradient(135deg, #f0f9ff, #eff6ff);
}

.signature-card .result-card-header {
    background: linear-gradient(135deg, #f0fdf4, #f7fee7);
}

/* JWT Summary Table */
.jwt-summary-table {
    background: #ffffff;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(74, 222, 128, 0.1);
    border: 1px solid var(--border-color);
}

.summary-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-color);
}

.jwt-info-table {
    margin-bottom: 0;
    font-size: 0.95rem;
}

.jwt-info-table tbody tr {
    border-bottom: 1px solid #e5e7eb;
    transition: background-color 0.2s ease;
}

.jwt-info-table tbody tr:hover {
    background-color: rgba(74, 222, 128, 0.02);
}

.jwt-info-table tbody tr:last-child {
    border-bottom: none;
}

.jwt-info-table tbody tr:nth-child(even) .info-label {
    background-color: #f9fafb;
}

.jwt-info-table tbody tr:nth-child(even) .info-value {
    background-color: #f9fafb;
}

.info-label {
    font-weight: 600;
    color: #374151;
    background-color: #f3f4f6;
    padding: 0.8rem 1rem;
    width: 180px;
    border-right: 1px solid #d1d5db;
    vertical-align: middle;
    font-size: 0.9rem;
}

.info-value {
    padding: 0.8rem 1rem;
    color: #6b7280;
    font-family: 'Roboto Mono', monospace;
    word-break: break-all;
    vertical-align: middle;
    background-color: #ffffff;
    font-size: 0.9rem;
}

.timestamp-readable {
    display: block;
    font-family: 'Roboto', sans-serif;
    font-size: 0.8rem;
    color: #9ca3af;
    margin-top: 0.3rem;
    font-style: normal;
    padding: 0.2rem 0.5rem;
    background-color: #f3f4f6;
    border-radius: 4px;
    border-left: 3px solid var(--primary-color);
}

/* Responsive table */
@media (max-width: 768px) {
    .jwt-info-table {
        font-size: 0.85rem;
    }

    .info-label {
        width: 140px;
        padding: 0.8rem 1rem;
        font-size: 0.85rem;
    }

    .info-value {
        padding: 0.8rem 1rem;
        font-size: 0.85rem;
    }

    .timestamp-readable {
        font-size: 0.75rem;
    }
}

.json-output .json-key {
    color: #e83e8c;
}

.json-output .json-string {
    color: #28a745;
}

.json-output .json-number {
    color: #007bff;
}

.json-output .json-boolean {
    color: #ff6b6b;
}

.json-output .json-null {
    color: #6c757d;
}

.signature-text {
    font-family: 'Roboto Mono', monospace;
    word-break: break-all;
    background-color: #f8f9fa;
    padding: 1.2rem;
    border-radius: 8px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12);
}

.card-header {
    border-bottom: none;
    background-color: #f8f9fa;
    padding: 1rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* JWT Structure Visualization */
.jwt-structure-visual {
    background: #fff;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow);
    animation: fadeIn 0.8s ease-out;
}

.jwt-part {
    margin-bottom: 1.2rem;
    padding: 1.2rem;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.jwt-part:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.jwt-part-label {
    font-weight: 700;
    margin-bottom: 0.8rem;
    font-size: 1rem;
    letter-spacing: 1px;
}

.jwt-part-content {
    font-family: 'Roboto Mono', monospace;
    font-size: 0.85rem;
    word-break: break-all;
}

.jwt-dot {
    font-size: 2.5rem;
    text-align: center;
    font-weight: bold;
    margin: 0.5rem 0;
    color: var(--accent-color);
}

.header {
    background-color: var(--header-color);
    color: white;
}

.payload {
    background-color: var(--payload-color);
    color: #333;
}

.signature {
    background-color: var(--signature-color);
    color: white;
}

/* Platform Cards */
.platform-card {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: var(--shadow);
    padding: 2rem;
    height: 100%;
    transition: all 0.3s ease;
}

.platform-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--hover-shadow);
}

.platform-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--accent-color);
    text-align: center;
}

.platform-card h3 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    text-align: center;
    font-weight: 600;
}

.code-snippet {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.2rem;
    margin: 1.2rem 0;
    overflow-x: auto;
    position: relative;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}

.code-snippet::before {
    content: 'Code';
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--accent-color);
    color: white;
    padding: 0.2rem 0.8rem;
    font-size: 0.7rem;
    border-radius: 0 8px 0 8px;
}

.code-snippet pre {
    margin: 0;
    font-family: 'Roboto Mono', monospace;
    font-size: 0.85rem;
    color: #333;
}

.platform-card .btn {
    width: 100%;
    transition: all 0.3s ease;
}

.platform-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

/* Feature Boxes */
.feature-box {
    padding: 2rem;
    border-radius: 15px;
    background-color: #fff;
    box-shadow: var(--shadow);
    height: 100%;
    transition: all 0.3s ease;
}

.feature-box:hover {
    transform: translateY(-8px);
    box-shadow: var(--hover-shadow);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--accent-color);
}

.feature-box h3 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.feature-list {
    padding-left: 1.8rem;
}

.feature-list li {
    margin-bottom: 0.8rem;
    position: relative;
}

.feature-list li::before {
    content: '✓';
    color: var(--accent-color);
    position: absolute;
    left: -1.3rem;
    font-weight: bold;
}

/* Article Cards */
.article-card {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: var(--shadow);
    overflow: hidden;
    height: 100%;
    transition: all 0.3s ease;
}

.article-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--hover-shadow);
}

.article-icon {
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.article-card:hover .article-icon i {
    transform: scale(1.1);
}

.article-icon i {
    transition: all 0.3s ease;
}

.article-content {
    padding: 1.8rem;
}

.article-content h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.4;
}

.article-content p {
    color: #555;
    margin-bottom: 1.5rem;
}

.article-content .btn-link {
    font-weight: 600;
    padding: 0;
    transition: all 0.3s ease;
    color: var(--accent-color);
}

.article-content .btn-link:hover {
    text-decoration: none;
    color: var(--primary-color);
}

.article-content .btn-link i {
    transition: all 0.3s ease;
}

.article-content .btn-link:hover i {
    transform: translateX(5px);
}

/* Newsletter Section */
.newsletter-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    padding: 3.5rem 0;
    margin: 0 1rem;
}

.newsletter-form .input-group {
    max-width: 550px;
    margin: 0 auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border-radius: 50px;
    overflow: hidden;
}

.newsletter-form .form-control {
    border: none;
    padding: 0.8rem 1.5rem;
    font-size: 1.05rem;
    height: auto;
}

.newsletter-form .btn {
    padding: 0.8rem 2rem;
    border-radius: 0 50px 50px 0;
    font-weight: 600;
    box-shadow: none;
}

/* Copy Button Styles */
.copy-btn {
    transition: all 0.3s;
    border-radius: 5px;
    padding: 0.3rem 0.8rem;
}

.copy-btn:hover {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.copy-btn.copied {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
}

/* Section Styles */
section {
    padding: 5rem 0;
}

section h2 {
    font-size: 2.3rem;
    font-weight: 700;
    margin-bottom: 3rem;
    position: relative;
    display: inline-block;
}

section h2:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add animations to elements */
.platform-card, .feature-box, .article-card {
    animation: fadeInUp 0.8s ease-out;
    animation-fill-mode: both;
}

.platform-card:nth-child(1) { animation-delay: 0.1s; }
.platform-card:nth-child(2) { animation-delay: 0.2s; }
.platform-card:nth-child(3) { animation-delay: 0.3s; }
.platform-card:nth-child(4) { animation-delay: 0.4s; }

.feature-box:nth-child(1) { animation-delay: 0.1s; }
.feature-box:nth-child(2) { animation-delay: 0.2s; }
.feature-box:nth-child(3) { animation-delay: 0.3s; }
.feature-box:nth-child(4) { animation-delay: 0.4s; }

.article-card:nth-child(1) { animation-delay: 0.1s; }
.article-card:nth-child(2) { animation-delay: 0.2s; }
.article-card:nth-child(3) { animation-delay: 0.3s; }
.article-card:nth-child(4) { animation-delay: 0.4s; }

/* Responsive Styles */
@media (max-width: 1200px) {
    .main-decoder-card {
        padding: 2.5rem;
    }
}

@media (max-width: 992px) {
    .decoder-main-title {
        font-size: 2.2rem;
    }

    .main-decoder-card {
        padding: 2rem;
    }

    .jwt-input {
        min-height: 120px;
        padding: 1.2rem;
    }
}

@media (max-width: 768px) {
    .decoder-section {
        padding-top: 1rem;
    }

    .decoder-main-title {
        font-size: 1.8rem;
    }

    .main-decoder-card {
        padding: 1.5rem;
        margin: 1rem;
    }

    .jwt-input {
        min-height: 100px;
        padding: 1rem;
        font-size: 0.9rem;
    }

    .btn-decode {
        padding: 0.7rem 1.5rem;
        font-size: 1rem;
    }

    .result-card-header {
        padding: 1rem;
    }

    .result-card-body {
        padding: 1rem;
    }

    .json-display,
    .signature-display {
        font-size: 0.8rem;
        padding: 1rem;
    }

    .results-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .main-decoder-card {
        margin: 0.5rem;
        padding: 1rem;
    }

    .decoder-main-title {
        font-size: 1.6rem;
    }

    .decoder-subtitle {
        font-size: 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a202c;
        color: #f8f9fa;
    }
    
    .decoder-card,
    .platform-card,
    .feature-box,
    .article-card,
    .jwt-structure-visual,
    .card {
        background-color: var(--dark-color);
        color: var(--light-color);
    }
    
    .json-output,
    .signature-text,
    .code-snippet,
    .article-icon,
    .card-header {
        background-color: #1a202c;
        color: var(--light-color);
    }
    
    .decoder-title {
        color: var(--light-color);
    }
    
    .newsletter-section {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    }
    
    #jwt-token-input {
        background-color: #1a202c;
        color: var(--light-color);
        border-color: #4a5568;
    }
    
    .feature-box h3, .platform-card h3, .article-content h3 {
        color: var(--light-color);
    }
    
    .article-content p {
        color: #cbd5e0;
    }
}

.text-primary-light {
    color: #ffffff;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
    font-weight: 700;
} 