/* JWT Decoder CSS */

/* Global Colors */
:root {
    --primary-color: #4ade80;
    --secondary-color: #86efac;
    --accent-color: #059669;
    --header-color: #10b981;
    --payload-color: #6ee7b7;
    --signature-color: #34d399;
    --dark-color: #166534;
    --light-color: #f0fdf4;
    --shadow: 0 5px 20px rgba(16, 185, 129, 0.15);
    --hover-shadow: 0 10px 25px rgba(16, 185, 129, 0.2);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    padding: 4rem 0;
    color: #fff;
    border-radius: 0 0 30px 30px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(5, 150, 105, 0.25);
}

.hero-section::before {
    content: '';
    position: absolute;
    width: 150%;
    height: 100%;
    background: rgba(255, 255, 255, 0.08);
    transform: rotate(-45deg);
    top: -50%;
    left: -25%;
    pointer-events: none;
}

.hero-section::after {
    content: '';
    position: absolute;
    width: 150%;
    height: 100%;
    background: rgba(255, 255, 255, 0.05);
    transform: rotate(45deg);
    bottom: -50%;
    right: -25%;
    pointer-events: none;
}

/* Floating elements to create depth */
.hero-section .container::before {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
    border-radius: 50%;
    top: 10%;
    left: 5%;
    animation: float 15s infinite ease-in-out;
}

.hero-section .container::after {
    content: '';
    position: absolute;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
    border-radius: 50%;
    bottom: 10%;
    right: 5%;
    animation: float 20s infinite ease-in-out reverse;
}

@keyframes float {
    0%, 100% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(20px, 10px);
    }
    50% {
        transform: translate(0, 20px);
    }
    75% {
        transform: translate(-10px, 10px);
    }
}

.hero-title {
    font-weight: 700;
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.3px;
    line-height: 1.3;
    color: #ffffff;
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.95;
    margin-bottom: 2rem;
    font-weight: 400;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.hero-section .btn {
    padding: 1rem 2.5rem;
    font-size: 1.2rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
    transition: all 0.3s ease;
    border-radius: 30px;
    letter-spacing: 0.3px;
    border: none;
    background: #ffffff;
    color: var(--accent-color);
}

.hero-section .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(5, 150, 105, 0.4);
    background: #f8f9fa;
}

.text-highlight {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.25);
    padding: 0.1em 0.3em;
    border-radius: 8px;
    margin: 0 -0.1em;
    position: relative;
    display: inline-block;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.text-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.2) 100%);
    border-radius: 8px;
    z-index: -1;
    opacity: 0.7;
    animation: shimmer 2.5s infinite linear;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Decoder Card */
.decoder-card {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: var(--shadow);
    padding: 2.5rem;
    margin-bottom: 3rem;
    transition: all 0.3s ease;
    transform: translateY(0);
    animation: fadeIn 0.8s ease-out;
}

.decoder-card:hover {
    box-shadow: var(--hover-shadow);
}

.decoder-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1.8rem;
    color: #333;
    position: relative;
    padding-bottom: 0.8rem;
}

.decoder-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

#jwt-token-input {
    font-family: 'Roboto Mono', monospace;
    min-height: 120px;
    font-size: 0.95rem;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

#jwt-token-input:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(94, 114, 228, 0.2);
}

.decoder-output {
    margin-top: 2.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #eee;
    animation: fadeIn 0.5s ease-out;
}

.form-group .btn {
    padding: 0.6rem 1.5rem;
    transition: all 0.3s ease;
}

.form-group .btn:hover {
    transform: translateY(-2px);
}

.json-output {
    background-color: #f8f9fa;
    padding: 1.2rem;
    overflow-x: auto;
    font-family: 'Roboto Mono', monospace;
    font-size: 0.95rem;
    max-height: 300px;
    overflow-y: auto;
    border-radius: 8px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}

.json-output .json-key {
    color: #e83e8c;
}

.json-output .json-string {
    color: #28a745;
}

.json-output .json-number {
    color: #007bff;
}

.json-output .json-boolean {
    color: #ff6b6b;
}

.json-output .json-null {
    color: #6c757d;
}

.signature-text {
    font-family: 'Roboto Mono', monospace;
    word-break: break-all;
    background-color: #f8f9fa;
    padding: 1.2rem;
    border-radius: 8px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12);
}

.card-header {
    border-bottom: none;
    background-color: #f8f9fa;
    padding: 1rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* JWT Structure Visualization */
.jwt-structure-visual {
    background: #fff;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow);
    animation: fadeIn 0.8s ease-out;
}

.jwt-part {
    margin-bottom: 1.2rem;
    padding: 1.2rem;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.jwt-part:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.jwt-part-label {
    font-weight: 700;
    margin-bottom: 0.8rem;
    font-size: 1rem;
    letter-spacing: 1px;
}

.jwt-part-content {
    font-family: 'Roboto Mono', monospace;
    font-size: 0.85rem;
    word-break: break-all;
}

.jwt-dot {
    font-size: 2.5rem;
    text-align: center;
    font-weight: bold;
    margin: 0.5rem 0;
    color: var(--accent-color);
}

.header {
    background-color: var(--header-color);
    color: white;
}

.payload {
    background-color: var(--payload-color);
    color: #333;
}

.signature {
    background-color: var(--signature-color);
    color: white;
}

/* Platform Cards */
.platform-card {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: var(--shadow);
    padding: 2rem;
    height: 100%;
    transition: all 0.3s ease;
}

.platform-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--hover-shadow);
}

.platform-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--accent-color);
    text-align: center;
}

.platform-card h3 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    text-align: center;
    font-weight: 600;
}

.code-snippet {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.2rem;
    margin: 1.2rem 0;
    overflow-x: auto;
    position: relative;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}

.code-snippet::before {
    content: 'Code';
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--accent-color);
    color: white;
    padding: 0.2rem 0.8rem;
    font-size: 0.7rem;
    border-radius: 0 8px 0 8px;
}

.code-snippet pre {
    margin: 0;
    font-family: 'Roboto Mono', monospace;
    font-size: 0.85rem;
    color: #333;
}

.platform-card .btn {
    width: 100%;
    transition: all 0.3s ease;
}

.platform-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

/* Feature Boxes */
.feature-box {
    padding: 2rem;
    border-radius: 15px;
    background-color: #fff;
    box-shadow: var(--shadow);
    height: 100%;
    transition: all 0.3s ease;
}

.feature-box:hover {
    transform: translateY(-8px);
    box-shadow: var(--hover-shadow);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--accent-color);
}

.feature-box h3 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.feature-list {
    padding-left: 1.8rem;
}

.feature-list li {
    margin-bottom: 0.8rem;
    position: relative;
}

.feature-list li::before {
    content: '✓';
    color: var(--accent-color);
    position: absolute;
    left: -1.3rem;
    font-weight: bold;
}

/* Article Cards */
.article-card {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: var(--shadow);
    overflow: hidden;
    height: 100%;
    transition: all 0.3s ease;
}

.article-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--hover-shadow);
}

.article-icon {
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.article-card:hover .article-icon i {
    transform: scale(1.1);
}

.article-icon i {
    transition: all 0.3s ease;
}

.article-content {
    padding: 1.8rem;
}

.article-content h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.4;
}

.article-content p {
    color: #555;
    margin-bottom: 1.5rem;
}

.article-content .btn-link {
    font-weight: 600;
    padding: 0;
    transition: all 0.3s ease;
    color: var(--accent-color);
}

.article-content .btn-link:hover {
    text-decoration: none;
    color: var(--primary-color);
}

.article-content .btn-link i {
    transition: all 0.3s ease;
}

.article-content .btn-link:hover i {
    transform: translateX(5px);
}

/* Newsletter Section */
.newsletter-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    padding: 3.5rem 0;
    margin: 0 1rem;
}

.newsletter-form .input-group {
    max-width: 550px;
    margin: 0 auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border-radius: 50px;
    overflow: hidden;
}

.newsletter-form .form-control {
    border: none;
    padding: 0.8rem 1.5rem;
    font-size: 1.05rem;
    height: auto;
}

.newsletter-form .btn {
    padding: 0.8rem 2rem;
    border-radius: 0 50px 50px 0;
    font-weight: 600;
    box-shadow: none;
}

/* Copy Button Styles */
.copy-btn {
    transition: all 0.3s;
    border-radius: 5px;
    padding: 0.3rem 0.8rem;
}

.copy-btn:hover {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.copy-btn.copied {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
}

/* Section Styles */
section {
    padding: 5rem 0;
}

section h2 {
    font-size: 2.3rem;
    font-weight: 700;
    margin-bottom: 3rem;
    position: relative;
    display: inline-block;
}

section h2:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add animations to elements */
.platform-card, .feature-box, .article-card {
    animation: fadeInUp 0.8s ease-out;
    animation-fill-mode: both;
}

.platform-card:nth-child(1) { animation-delay: 0.1s; }
.platform-card:nth-child(2) { animation-delay: 0.2s; }
.platform-card:nth-child(3) { animation-delay: 0.3s; }
.platform-card:nth-child(4) { animation-delay: 0.4s; }

.feature-box:nth-child(1) { animation-delay: 0.1s; }
.feature-box:nth-child(2) { animation-delay: 0.2s; }
.feature-box:nth-child(3) { animation-delay: 0.3s; }
.feature-box:nth-child(4) { animation-delay: 0.4s; }

.article-card:nth-child(1) { animation-delay: 0.1s; }
.article-card:nth-child(2) { animation-delay: 0.2s; }
.article-card:nth-child(3) { animation-delay: 0.3s; }
.article-card:nth-child(4) { animation-delay: 0.4s; }

/* Responsive Styles */
@media (max-width: 992px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    section h2 {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-section {
        padding: 4rem 0;
    }
    
    section {
        padding: 4rem 0;
    }
    
    .decoder-card {
        padding: 1.8rem;
    }
    
    .jwt-structure-visual {
        margin-bottom: 2.5rem;
    }
    
    .platform-card, .feature-box, .article-card {
        margin-bottom: 2rem;
    }
    
    section h2 {
        font-size: 1.8rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a202c;
        color: #f8f9fa;
    }
    
    .decoder-card,
    .platform-card,
    .feature-box,
    .article-card,
    .jwt-structure-visual,
    .card {
        background-color: var(--dark-color);
        color: var(--light-color);
    }
    
    .json-output,
    .signature-text,
    .code-snippet,
    .article-icon,
    .card-header {
        background-color: #1a202c;
        color: var(--light-color);
    }
    
    .decoder-title {
        color: var(--light-color);
    }
    
    .newsletter-section {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    }
    
    #jwt-token-input {
        background-color: #1a202c;
        color: var(--light-color);
        border-color: #4a5568;
    }
    
    .feature-box h3, .platform-card h3, .article-content h3 {
        color: var(--light-color);
    }
    
    .article-content p {
        color: #cbd5e0;
    }
}

.text-primary-light {
    color: #ffffff;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
    font-weight: 700;
} 