{% extends "base.html" %}

{% block title %}JWT Decode Offline | Download JWT Decoder for Local Use{% endblock %}

{% block description %}Download our JWT decoder for offline use. Decode JWT tokens locally with no internet connection required. Available for desktop, mobile, and as a browser extension.{% endblock %}

{% block meta_keywords %}jwt decode offline, jwt decode download, jwt decode android, jwt decode locally, jwt decode github, jwt decoder offline{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
<link href="{{ url_for('static', filename='css/jwt-decoder.css') }}" rel="stylesheet">
<style>
    .offline-feature {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    .offline-feature:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .platform-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: var(--bs-primary);
    }
    .download-card {
        border-radius: 10px;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
    }
    .download-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .breadcrumb {
        margin-bottom: 2rem;
    }
    .version-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        margin-left: 10px;
        background-color: var(--bs-primary);
        color: white;
    }
    .download-stats {
        font-size: 0.9rem;
        color: #666;
        margin-top: 0.5rem;
    }
    #jwt-offline-decoder {
        margin-bottom: 3rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="#">Tools</a></li>
        <li class="breadcrumb-item active" aria-current="page">JWT Decode Offline</li>
    </ol>
</nav>

<!-- Hero Section -->
<section class="hero-section text-center py-4">
    <div class="container">
        <h1 class="hero-title mb-3">JWT <span class="text-highlight">Decode</span> Offline</h1>
        <p class="hero-subtitle mb-4">Decode JWT tokens securely offline - no internet connection required</p>
    </div>
</section>

<!-- Decoder Tool Section -->
<section id="jwt-offline-decoder" class="py-4">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="decoder-card">
                    <h2 class="decoder-title">Offline JWT Decoder</h2>
                    <p class="text-muted mb-4">This tool works 100% offline - your tokens never leave your device</p>
                    
                    <div class="form-group mb-4">
                        <textarea id="jwt-token-input" class="form-control" placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c" rows="4"></textarea>
                        <div class="token-info text-muted mt-1 small">
                            <i class="fas fa-info-circle"></i> Enter your JWT token above to decode it instantly
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="keyboard-shortcuts small text-muted">
                                <span data-tippy-content="Press Ctrl+Enter to decode"><i class="fas fa-keyboard me-1"></i> Shortcuts: Ctrl+Enter to decode</span>
                            </div>
                            <div class="action-buttons">
                                <button id="decode-button" class="btn btn-primary" data-tippy-content="Decode the JWT token">
                                    <i class="fas fa-key me-1"></i> Decode
                                </button>
                                <button id="clear-button" class="btn btn-outline-secondary ms-2" data-tippy-content="Clear the input and results">
                                    <i class="fas fa-times me-1"></i> Clear
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Decoder Output -->
                    <div id="decoded-output" class="decoder-output" style="display: none;">
                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h3 class="mb-0">Header</h3>
                                        <button class="btn btn-sm btn-outline-primary copy-btn" data-target="header-json" data-tippy-content="Copy header to clipboard">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <pre id="header-json" class="json-output language-json rounded-3"></pre>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-12 mb-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h3 class="mb-0">Payload</h3>
                                        <div>
                                            <span id="payload-claims-count" class="badge bg-info me-2">0 claims</span>
                                            <button class="btn btn-sm btn-outline-primary copy-btn" data-target="payload-json" data-tippy-content="Copy payload to clipboard">
                                                <i class="fas fa-copy"></i> Copy
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <pre id="payload-json" class="json-output language-json rounded-3"></pre>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-12 mb-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h3 class="mb-0">Signature</h3>
                                        <button class="btn btn-sm btn-outline-primary copy-btn" data-target="signature-data" data-tippy-content="Copy signature to clipboard">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <p id="signature-verification-status" class="mb-3"></p>
                                        <div id="signature-data" class="signature-text"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="mb-0">Token Analysis</h3>
                                    </div>
                                    <div class="card-body">
                                        <table id="token-analysis" class="table table-sm">
                                            <tbody>
                                                <tr>
                                                    <th scope="row">Algorithm</th>
                                                    <td id="token-algorithm">-</td>
                                                </tr>
                                                <tr>
                                                    <th scope="row">Issued At</th>
                                                    <td id="token-issued-at">-</td>
                                                </tr>
                                                <tr>
                                                    <th scope="row">Expiration</th>
                                                    <td id="token-expiration">-</td>
                                                </tr>
                                                <tr>
                                                    <th scope="row">Status</th>
                                                    <td id="token-status">-</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="mb-0">Timeline</h3>
                                    </div>
                                    <div class="card-body">
                                        <div id="token-timeline" class="p-2">
                                            <!-- Timeline will be generated by JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Download Options Section -->
<section class="download-section py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">Download JWT Decoder for Offline Use</h2>
        
        <div class="row">
            <!-- Desktop Apps -->
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="download-card card h-100">
                    <div class="card-body text-center">
                        <div class="platform-icon">
                            <i class="fas fa-desktop"></i>
                        </div>
                        <h3>Desktop App <span class="version-badge">v1.2.0</span></h3>
                        <p>Decode JWT tokens with our full-featured desktop application</p>
                        <div class="download-links mt-3">
                            <a href="#" class="btn btn-outline-primary mb-2 w-100"><i class="fab fa-windows me-2"></i> Windows</a>
                            <a href="#" class="btn btn-outline-primary mb-2 w-100"><i class="fab fa-apple me-2"></i> macOS</a>
                            <a href="#" class="btn btn-outline-primary w-100"><i class="fab fa-linux me-2"></i> Linux</a>
                        </div>
                        <div class="download-stats">
                            <i class="fas fa-download me-1"></i> 25K+ downloads
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Mobile Apps -->
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="download-card card h-100">
                    <div class="card-body text-center">
                        <div class="platform-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3>Mobile App <span class="version-badge">v1.1.5</span></h3>
                        <p>Decode tokens on the go with our mobile applications</p>
                        <div class="download-links mt-3">
                            <a href="#" class="btn btn-outline-primary mb-2 w-100"><i class="fab fa-android me-2"></i> Android</a>
                            <a href="#" class="btn btn-outline-primary w-100"><i class="fab fa-apple me-2"></i> iOS</a>
                        </div>
                        <div class="download-stats">
                            <i class="fas fa-download me-1"></i> 15K+ downloads
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Browser Extensions -->
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="download-card card h-100">
                    <div class="card-body text-center">
                        <div class="platform-icon">
                            <i class="fas fa-puzzle-piece"></i>
                        </div>
                        <h3>Browser Extension <span class="version-badge">v1.3.1</span></h3>
                        <p>Decode JWT tokens directly in your browser with one click</p>
                        <div class="download-links mt-3">
                            <a href="#" class="btn btn-outline-primary mb-2 w-100"><i class="fab fa-chrome me-2"></i> Chrome</a>
                            <a href="#" class="btn btn-outline-primary mb-2 w-100"><i class="fab fa-firefox me-2"></i> Firefox</a>
                            <a href="#" class="btn btn-outline-primary w-100"><i class="fab fa-edge me-2"></i> Edge</a>
                        </div>
                        <div class="download-stats">
                            <i class="fas fa-download me-1"></i> 50K+ users
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Source Code -->
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="download-card card h-100">
                    <div class="card-body text-center">
                        <div class="platform-icon">
                            <i class="fab fa-github"></i>
                        </div>
                        <h3>Source Code <span class="version-badge">MIT</span></h3>
                        <p>Get the source code and contribute to our open-source project</p>
                        <div class="download-links mt-3">
                            <a href="#" class="btn btn-outline-primary mb-2 w-100"><i class="fab fa-github me-2"></i> GitHub Repo</a>
                            <a href="#" class="btn btn-outline-primary mb-2 w-100"><i class="fas fa-file-archive me-2"></i> Download ZIP</a>
                            <a href="#" class="btn btn-outline-primary w-100"><i class="fas fa-book me-2"></i> Documentation</a>
                        </div>
                        <div class="download-stats">
                            <i class="fas fa-star me-1"></i> 1.2K+ stars
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <h2 class="text-center mb-5">Why Use Our Offline JWT Decoder?</h2>
        
        <div class="row">
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="offline-feature bg-light">
                    <div class="text-center mb-3">
                        <i class="fas fa-lock fa-3x text-primary"></i>
                    </div>
                    <h3 class="text-center mb-3">Enhanced Security</h3>
                    <p>Your tokens never leave your device. All processing happens locally, ensuring your sensitive data remains secure.</p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="offline-feature bg-light">
                    <div class="text-center mb-3">
                        <i class="fas fa-wifi-slash fa-3x text-primary"></i>
                    </div>
                    <h3 class="text-center mb-3">Works Offline</h3>
                    <p>No internet connection required. Decode JWT tokens anytime, anywhere, even in air-gapped environments.</p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="offline-feature bg-light">
                    <div class="text-center mb-3">
                        <i class="fas fa-bolt fa-3x text-primary"></i>
                    </div>
                    <h3 class="text-center mb-3">Lightning Fast</h3>
                    <p>Instant decoding without network latency. Optimized for performance on all platforms.</p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="offline-feature bg-light">
                    <div class="text-center mb-3">
                        <i class="fas fa-cogs fa-3x text-primary"></i>
                    </div>
                    <h3 class="text-center mb-3">Advanced Features</h3>
                    <p>Token validation, expiration checking, and visual representation of claims in an intuitive interface.</p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="offline-feature bg-light">
                    <div class="text-center mb-3">
                        <i class="fas fa-code fa-3x text-primary"></i>
                    </div>
                    <h3 class="text-center mb-3">Developer Friendly</h3>
                    <p>Open-source, well-documented, and easily extensible. Use our code in your own applications.</p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="offline-feature bg-light">
                    <div class="text-center mb-3">
                        <i class="fas fa-sync-alt fa-3x text-primary"></i>
                    </div>
                    <h3 class="text-center mb-3">Regular Updates</h3>
                    <p>Frequent updates with new features, security improvements, and support for new JWT standards.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Integration Examples Section -->
<section class="integration-examples py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">Integration Examples</h2>
        
        <div class="row">
            <!-- JavaScript Example -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h3 class="mb-0"><i class="fab fa-js me-2"></i> JavaScript</h3>
                    </div>
                    <div class="card-body">
                        <pre class="language-javascript rounded"><code>// Using the jwt-decode library
import jwtDecode from 'jwt-decode';

// Decode the token
try {
  const decodedToken = jwtDecode(token);
  console.log(decodedToken);
  
  // Access specific claims
  const userId = decodedToken.sub;
  const expiryTime = decodedToken.exp;
  
  // Check if token is expired
  const isExpired = expiryTime < Date.now() / 1000;
} catch (error) {
  console.error('Invalid token:', error);
}</code></pre>
                    </div>
                </div>
            </div>
            
            <!-- Node.js Example -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h3 class="mb-0"><i class="fab fa-node-js me-2"></i> Node.js</h3>
                    </div>
                    <div class="card-body">
                        <pre class="language-javascript rounded"><code>// Using the jsonwebtoken library
const jwt = require('jsonwebtoken');

// Decode without verification
const decoded = jwt.decode(token);
console.log(decoded);

// Decode with verification (if you have the secret)
try {
  const verified = jwt.verify(token, 'your-secret-key');
  console.log('Token is valid:', verified);
} catch (error) {
  if (error instanceof jwt.TokenExpiredError) {
    console.log('Token has expired');
  } else {
    console.log('Token verification failed:', error.message);
  }
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq-section py-5">
    <div class="container">
        <h2 class="text-center mb-5">Frequently Asked Questions</h2>
        
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingOne">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                Is the offline JWT decoder truly secure?
                            </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>Yes, our offline JWT decoder is designed with security as a top priority. All token processing happens locally on your device, and no data is ever sent to our servers or any third parties. This makes it ideal for working with sensitive tokens that contain confidential information.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingTwo">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                Do I need an internet connection to use the offline decoder?
                            </button>
                        </h2>
                        <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>No, once you've downloaded the offline JWT decoder, it works completely without an internet connection. This makes it ideal for secure environments, air-gapped systems, or situations where internet access is limited or unavailable.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingThree">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                Which JWT algorithms are supported?
                            </button>
                        </h2>
                        <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>Our JWT decoder supports all standard JWT algorithms, including:</p>
                                <ul>
                                    <li>HMAC algorithms: HS256, HS384, HS512</li>
                                    <li>RSA algorithms: RS256, RS384, RS512</li>
                                    <li>ECDSA algorithms: ES256, ES384, ES512</li>
                                    <li>RSA-PSS algorithms: PS256, PS384, PS512</li>
                                    <li>None algorithm (for unsigned tokens, use with caution)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingFour">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                                Can I verify token signatures with the offline decoder?
                            </button>
                        </h2>
                        <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>Yes, the desktop and mobile applications allow you to verify token signatures if you provide the appropriate secret key (for HMAC algorithms) or public key (for RSA and ECDSA algorithms). The browser extension has more limited verification capabilities due to security considerations.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingFive">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                                Is the source code available for inspection?
                            </button>
                        </h2>
                        <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>Yes, our JWT decoder is open source and available on GitHub. You can inspect the code, contribute improvements, or even fork the project for your own needs. We believe in transparency, especially for security tools.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<!-- 首先加载Popper依赖 -->
<script src="https://unpkg.com/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
<!-- 然后加载Tippy.js -->
<script src="https://unpkg.com/tippy.js@6.3.7/dist/tippy-bundle.umd.min.js"></script>
<!-- 其他脚本 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<!-- 最后加载我们的应用脚本 -->
<script src="{{ url_for('static', filename='js/jwt-offline-decoder.js') }}"></script>
{% endblock %} 