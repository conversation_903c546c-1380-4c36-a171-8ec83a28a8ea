/* JWT Generator Styles */

/* Main container styles */
.generator-card {
    background-color: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.generator-title {
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 25px;
    text-align: center;
}

/* Header, Payload, Signature sections */
.card-header {
    background-color: rgba(74, 107, 175, 0.1);
    border-bottom: none;
    padding: 15px 20px;
}

.card-header h3 {
    color: var(--primary-color);
    font-size: 1.3rem;
    margin: 0;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

/* Form controls */
.form-label {
    font-weight: 500;
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 6px;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #e1e5eb;
    padding: 10px 15px;
    box-shadow: none;
    transition: all 0.3s;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(74, 107, 175, 0.15);
}

.form-control::placeholder {
    color: #b2b9c5;
    font-size: 0.9rem;
}

/* JSON Preview */
.json-preview {
    background-color: #f7f9fc;
    color: #333;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    border: 1px solid #e1e5eb;
    max-height: 200px;
    overflow-y: auto;
}

/* Token visualization */
.token-visualization {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    margin: 15px 0 25px;
    font-family: 'Courier New', monospace;
}

.jwt-part {
    padding: 15px;
    border-radius: 8px;
    margin: 5px;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    max-width: 250px;
    overflow: hidden;
    cursor: pointer;
}

.jwt-part:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.jwt-part.header {
    background-color: #ff7675;
    color: white;
}

.jwt-part.payload {
    background-color: #74b9ff;
    color: white;
}

.jwt-part.signature {
    background-color: #55efc4;
    color: white;
}

.jwt-part-label {
    font-weight: 700;
    margin-bottom: 5px;
    font-size: 0.8rem;
    opacity: 0.9;
}

.jwt-part-content {
    font-size: 0.7rem;
    word-break: break-all;
}

.jwt-dot {
    font-size: 24px;
    font-weight: bold;
    margin: 0 5px;
    color: var(--text-color);
}

/* Hero section */
.hero-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e9f2 100%);
    padding: 60px 0;
    border-radius: 15px;
    margin-bottom: 30px;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-color);
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.text-highlight {
    color: var(--accent-color);
    font-weight: 700;
}

.text-primary-light {
    color: var(--primary-color);
}

/* Token info styles */
.token-info-item {
    background-color: #f7f9fc;
    padding: 10px 15px;
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.token-info-label {
    font-weight: 600;
    color: var(--text-color);
    margin-right: 5px;
}

.token-info-value {
    color: var(--primary-color);
}

/* How it works section */
.steps-card, .jwt-info-card, .jwt-tips-card {
    background-color: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    height: 100%;
}

.generation-steps {
    padding-left: 20px;
}

.generation-steps li {
    margin-bottom: 20px;
    position: relative;
}

.generation-steps li strong {
    display: block;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.code-example {
    background-color: #f7f9fc;
    padding: 10px;
    border-radius: 5px;
    border-left: 3px solid var(--accent-color);
    font-size: 0.85rem;
    margin-top: 8px;
    white-space: pre-wrap;
}

/* Algorithm types */
.algorithm-type h4 {
    color: var(--primary-color);
    font-size: 1.1rem;
    margin-bottom: 8px;
}

.algorithm-type p {
    margin-bottom: 8px;
}

.algorithm-type ul {
    padding-left: 20px;
}

.algorithm-type li {
    margin-bottom: 5px;
}

/* Security tips */
.security-tips {
    padding-left: 20px;
}

.security-tips li {
    margin-bottom: 10px;
    position: relative;
}

/* Use cases section */
.use-case-card {
    background-color: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    height: 100%;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.use-case-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.use-case-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.use-case-card h3 {
    font-size: 1.3rem;
    color: var(--text-color);
    margin-bottom: 10px;
}

.use-case-card p {
    color: var(--text-muted);
    font-size: 0.95rem;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .jwt-part {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 15px;
    }
    
    .jwt-dot {
        display: none;
    }
}

/* Animations */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(74, 107, 175, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(74, 107, 175, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(74, 107, 175, 0);
    }
}

#generate-token-btn {
    animation: pulse 2s infinite;
} 