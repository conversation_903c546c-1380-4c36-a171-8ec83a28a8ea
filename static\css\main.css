/* 
 * Calculadora de Horas - Main CSS
 * 主要样式文件
 */

/* General Styles */
:root {
    --primary-color: #4a6baf;
    --primary-hover: #3a5b9f;
    --secondary-color: #f5b342;
    --secondary-hover: #e5a332;
    --light-color: #f8f9fa;
    --dark-color: #333;
    --gray-color: #6c757d;
    --border-color: #ddd;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --text-color: #333;
    --light-text: #777;
    --background-color: #f9f9f9;
    --card-bg: #fff;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--background-color);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--accent-color);
    text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.text-accent {
    color: var(--accent-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #3a5b9f;
    border-color: #3a5b9f;
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--text-color);
}

.btn-secondary:hover, .btn-secondary:focus {
    background-color: #e5a332;
    border-color: #e5a332;
    color: var(--text-color);
}

.btn-accent {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

.btn-accent:hover, .btn-accent:focus {
    background-color: #d63a09;
    border-color: #d63a09;
    color: white;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.25rem;
    border-radius: 0.5rem;
}

/* Header & Navigation */
.navbar {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    padding: 10px 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: translateY(-2px);
    color: var(--primary-hover);
}

.navbar-brand i {
    margin-right: 5px;
    color: var(--accent-color);
}

.nav-link {
    color: var(--text-color);
    font-weight: 500;
    padding: 10px 15px;
    margin: 0 5px;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover, .nav-link.active {
    color: var(--primary-color);
    background-color: rgba(74, 107, 175, 0.1);
    transform: translateY(-2px);
}

.nav-link.active:after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 50%;
    width: 30px;
    height: 3px;
    background: var(--primary-color);
    transform: translateX(-50%);
    border-radius: 3px;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.dropdown-item {
    padding: 8px 15px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.dropdown-item:hover, .dropdown-item:focus {
    background-color: var(--primary-color);
    color: white;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 适配移动设备的导航栏 */
@media (max-width: 992px) {
    .navbar-collapse {
        background-color: white;
        padding: 1rem;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-top: 15px;
    }
    
    .nav-link.active:after {
        bottom: 0;
        left: 10px;
        width: 5px;
        height: 80%;
        transform: translateX(0);
    }
    
    .dropdown-menu {
        border: none;
        box-shadow: none;
        padding-left: 15px;
    }
}

/* Hero Section */
.hero-section {
    padding: 60px 0;
    background-color: #f0f4ff;
    border-radius: 0 0 30px 30px;
    margin-bottom: 30px;
}

.hero-section h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.hero-section .lead {
    font-size: 1.25rem;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 15px;
}

/* Для создания плейсхолдера изображения */
.hero-placeholder {
    width: 100%;
    height: 300px;
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    overflow: hidden;
}

/* BMI Calculator */
.bmi-calculator {
    background-color: white;
    border-radius: 1rem;
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
}

.bmi-calculator h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.bmi-calculator .form-label {
    font-weight: 500;
}

.bmi-calculator .btn-calculate {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    padding: 0.5rem 2rem;
    font-size: 1.1rem;
    margin-top: 1rem;
}

.bmi-calculator .btn-calculate:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
    transition: all 0.3s;
}

.gender-selector {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.gender-selector .gender-option {
    flex: 1;
    text-align: center;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s;
}

.gender-selector .gender-option.selected {
    border-color: var(--primary-color);
    background-color: rgba(0, 102, 204, 0.1);
}

.gender-selector .gender-option i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--gray-color);
}

.gender-selector .gender-option.selected i {
    color: var(--primary-color);
}

.gender-selector .gender-option span {
    font-weight: 500;
}

/* BMI Results */
.bmi-results {
    display: none;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px dashed var(--border-color);
}

.bmi-results.visible {
    display: block;
}

.bmi-results h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.bmi-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.bmi-category {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.bmi-scale {
    height: 2rem;
    background: linear-gradient(to right, #0073ff, #00c8ff, #00d82a, #ffbe0a, #ff5a00);
    border-radius: 1rem;
    position: relative;
    margin: 2rem 0;
}

.bmi-marker {
    position: absolute;
    top: -10px;
    width: 20px;
    height: 20px;
    background-color: white;
    border: 3px solid var(--dark-color);
    border-radius: 50%;
    transform: translateX(-50%);
}

.bmi-scale-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: var(--gray-color);
}

.bmi-recommendation {
    background-color: #e9f7ef;
    border-left: 4px solid var(--secondary-color);
    padding: 1.5rem;
    margin-top: 1.5rem;
    border-radius: 0.3rem;
}

.bmi-recommendation h4 {
    color: var(--secondary-color);
    margin-bottom: 0.8rem;
}

/* Info Boxes */
.info-box {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    height: 100%;
    transition: all 0.3s;
}

.info-box:hover {
    transform: translateY(-5px);
}

.info-box-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(0, 102, 204, 0.1);
    color: var(--primary-color);
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.info-box h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.info-box p {
    color: var(--gray-color);
    margin-bottom: 0;
}

/* Section Styles */
.section-title {
    text-align: center;
    margin-bottom: 40px;
}

.section-title h2 {
    font-size: 2rem;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.section-title h2:after {
    content: '';
    position: absolute;
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.section-title p {
    color: var(--gray-color);
    max-width: 700px;
    margin: 1rem auto 0;
}

.section {
    padding: 60px 0;
}

.section-gray {
    background-color: #f5f7fa;
}

/* FAQ Section */
.accordion-item {
    border: none;
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.accordion-button {
    background-color: white;
    font-weight: 600;
    color: var(--primary-color);
    padding: 1.2rem;
}

.accordion-button:not(.collapsed) {
    background-color: rgba(0, 102, 204, 0.05);
    color: var(--primary-color);
}

.accordion-button:focus {
    box-shadow: none;
    border-color: rgba(0, 102, 204, 0.1);
}

.accordion-body {
    padding: 1.2rem;
    color: var(--gray-color);
}

/* Blog Section */
.blog-card {
    background-color: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    height: 100%;
    transition: all 0.3s;
}

.blog-card:hover {
    transform: translateY(-5px);
}

.blog-card-img {
    height: 200px;
    object-fit: cover;
}

.blog-card-body {
    padding: 1.5rem;
}

.blog-card-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
    color: var(--primary-color);
}

.blog-card-text {
    color: var(--gray-color);
    margin-bottom: 1.2rem;
}

.blog-card-date {
    color: var(--gray-color);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

/* Footer Styles */
footer {
    background-color: #333;
    color: #fff;
}

footer h5 {
    font-size: 1.1rem;
    margin-bottom: 20px;
    color: #fff;
}

footer a {
    color: #fff;
    opacity: 0.8;
    transition: all 0.3s ease;
}

footer a:hover {
    opacity: 1;
    text-decoration: none;
}

.social-icons a {
    width: 36px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.social-icons a:hover {
    background-color: #4a8bfc;
    transform: translateY(-3px);
}

footer ul li {
    margin-bottom: 0.5rem;
}

footer hr {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .navbar {
        padding: 10px 0;
    }
    
    .section {
        padding: 3rem 0;
    }
    
    .section-title h2 {
        font-size: 1.75rem;
    }
    
    .bmi-calculator {
        padding: 1.5rem;
    }
    
    .gender-selector {
        flex-direction: column;
    }
    
    .gender-selector .gender-option {
        padding: 0.8rem;
    }
    
    .info-box {
        margin-bottom: 1.5rem;
    }
    
    .hero-section h1 {
        font-size: 1.75rem;
    }
}

/* 暗色模式切换 */
.dark-mode {
    --body-bg: #121212;
    --body-color: #e0e0e0;
    --dark-color: #f8f9fa;
    --light-color: #343a40;
}

.dark-mode .navbar,
.dark-mode .calculator-card,
.dark-mode .feature-card,
.dark-mode .step-card,
.dark-mode .article-card {
    background-color: #1e1e1e;
}

.dark-mode .hero-section {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.dark-mode .form-control {
    background-color: #2a2a2a;
    border-color: #444;
    color: #e0e0e0;
}

.dark-mode .result-box {
    background-color: #2a2a2a;
}

.dark-mode footer {
    background-color: #1e1e1e;
}

.dark-mode .calculator-card {
    background-color: #1e1e1e;
}

.dark-mode .calculator-card .border {
    border-color: #2a2a2a !important;
    background-color: #252525;
}

.dark-mode .calculator-card .form-control,
.dark-mode .calculator-card .form-select {
    background-color: #2a2a2a;
    border-color: #444;
    color: #e0e0e0;
}

.dark-mode .calculator-card .form-check-label {
    color: #e0e0e0;
}

/* 主题切换按钮 */
.theme-toggle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--light-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    background-color: var(--gray-color);
}

/* 月度计算器样式 */
.calendar-grid {
    display: grid;
    gap: 1px;
    background-color: #eee;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background-color: #f8f9fa;
}

.calendar-body {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

.calendar-cell {
    padding: 8px;
    min-height: 80px;
    background-color: white;
    border: 1px solid #eee;
}

.calendar-cell.header {
    text-align: center;
    font-weight: bold;
    min-height: auto;
    padding: 12px 8px;
    background-color: #f8f9fa;
}

.calendar-cell.empty {
    background-color: #f8f9fa;
}

.date-number {
    font-size: 14px;
    margin-bottom: 4px;
}

.hours-input {
    width: 100%;
    font-size: 14px;
}

.month-selector {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .calendar-cell {
        min-height: 60px;
        padding: 4px;
    }
    
    .date-number {
        font-size: 12px;
    }
    
    .hours-input {
        font-size: 12px;
        padding: 2px 4px;
    }
}

/* 内容区域样式 */
.content-section {
    background-color: #fff;
    padding: 4rem 0;
}

.content-block {
    margin-bottom: 3rem;
}

.content-block:last-child {
    margin-bottom: 0;
}

.feature-list {
    list-style: none;
    padding-left: 0;
}

.feature-list li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
}

.feature-list li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
}

.table-bordered {
    border-color: #e0e6ff;
}

.table-light {
    background-color: #f8f9ff;
}

.accordion-button:not(.collapsed) {
    background-color: #f8f9ff;
    color: var(--primary-color);
}

.accordion-button:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(74, 108, 247, 0.25);
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-body {
    padding: 2rem;
}

.card-title {
    color: var(--primary-color);
    font-weight: 700;
}

.alert-info {
    background-color: #f8f9ff;
    border-color: #e0e6ff;
    color: var(--body-color);
}

/* 暗色模式适配 */
.dark-mode .content-section {
    background-color: #1e1e1e;
}

.dark-mode .card {
    background-color: #2a2a2a;
    border-color: #444;
}

.dark-mode .table {
    color: #e0e0e0;
}

.dark-mode .table-bordered {
    border-color: #444;
}

.dark-mode .table-light {
    background-color: #2a2a2a;
}

.dark-mode .accordion-button {
    background-color: #2a2a2a;
    color: #e0e0e0;
}

.dark-mode .accordion-button:not(.collapsed) {
    background-color: #3a3a3a;
    color: var(--primary-color);
}

.dark-mode .alert-info {
    background-color: #2a2a2a;
    border-color: #444;
    color: #e0e0e0;
}

/* Dark Mode */
[data-theme="dark"] {
    --text-color: #f5f6fa;
    --light-bg: #2d3436;
    --white: #2d3436;
    --gray: #636e72;
}

[data-theme="dark"] .navbar,
[data-theme="dark"] .calculator-card,
[data-theme="dark"] .card,
[data-theme="dark"] .article-card {
    background-color: var(--white);
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: var(--light-bg);
    border-color: var(--gray);
    color: var(--text-color);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: var(--light-bg);
    color: var(--text-color);
}

[data-theme="dark"] .theme-toggle {
    background-color: var(--gray);
}

[data-theme="dark"] .theme-toggle:hover {
    background-color: var(--text-color);
}

/* 结果显示样式 */
.result-container {
    padding: 1.5rem;
    background-color: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.result-container .card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.result-container .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.result-container .card-header {
    border-radius: 10px 10px 0 0;
}

.result-container .table {
    margin-bottom: 0;
}

.result-container .fs-1 {
    color: var(--primary-color);
}

.result-container canvas {
    max-width: 100%;
    height: auto;
}

/* 暗色模式适配 */
[data-theme="dark"] .result-container {
    background-color: var(--white);
}

[data-theme="dark"] .result-container .card {
    background-color: var(--white);
}

[data-theme="dark"] .result-container .table {
    color: var(--text-color);
}

[data-theme="dark"] .result-container .card-header {
    background-color: var(--primary-color);
}

@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.2rem;
    }
    
    .hero-section {
        padding: 100px 0 80px;
    }
}

/* Oroscopo Styles */
.oroscopo-hero {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 60px 0;
    margin-bottom: 2rem;
}

.oroscopo-hero h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

.current-date {
    display: inline-block;
    padding: 8px 16px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    font-size: 1.1rem;
}

.segno-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.segno-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.1);
}

.nav-pills .nav-link {
    color: var(--text-color);
    border-radius: 30px;
    padding: 8px 20px;
    margin: 0 5px;
    font-weight: 600;
}

.nav-pills .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.oroscopo-content p {
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Dark Mode Styles */
.dark-mode .oroscopo-hero {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
}

.dark-mode .segno-card {
    background-color: #2a2a2a;
}

.dark-mode .nav-pills .nav-link {
    color: #e0e0e0;
}

.dark-mode .nav-pills .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.prediction-item {
    background-color: rgba(0,0,0,0.02);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
}

.prediction-label {
    color: var(--text-color);
    font-size: 0.95rem;
    margin-bottom: 4px;
}

.prediction-text {
    color: var(--text-color);
    font-size: 0.9rem;
    line-height: 1.5;
}

.dark-mode .prediction-item {
    background-color: rgba(255,255,255,0.05);
}

.dark-mode .prediction-label,
.dark-mode .prediction-text {
    color: rgba(255,255,255,0.9);
}

/* 星星动画样式 */
.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: white;
    border-radius: 50%;
    animation: twinkle 3s infinite;
}

@keyframes twinkle {
    0% { opacity: 0; }
    50% { opacity: 1; }
    100% { opacity: 0; }
}

/* 行星页面特定样式 */
.pianeti-page .hero-section {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    padding: 4rem 0;
}

.pianeti-page .sistema-solare-animation {
    position: relative;
    height: 300px;
    background: linear-gradient(to right, #000428, #004e92);
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.pianeti-page .pianeta-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.pianeti-page .pianeta-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.1);
}

.pianeti-page .caratteristica-item {
    background-color: rgba(0,0,0,0.02);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
}

.pianeti-page .caratteristica-label {
    font-weight: 600;
    color: #2d3436;
    margin-bottom: 4px;
}

.pianeti-page .caratteristica-text {
    color: #636e72;
}

[data-theme="dark"] .pianeti-page .caratteristica-label {
    color: #dfe6e9;
}

[data-theme="dark"] .pianeti-page .caratteristica-text {
    color: #b2bec3;
}

[data-theme="dark"] .pianeti-page .pianeta-card {
    background-color: #2d3436;
}

[data-theme="dark"] .pianeti-page .caratteristica-item {
    background-color: rgba(255,255,255,0.05);
}

/* 女性BMI计算器页面样式 */
.section-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
    color: white;
    padding: 4rem 0;
    margin-bottom: 3rem;
    border-radius: 0 0 2rem 2rem;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.2);
}

.section-header h1 {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header .lead {
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.bmi-calculator {
    background: white;
    border-radius: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 2.5rem;
    margin-top: -3rem;
    position: relative;
    z-index: 2;
}

.bmi-calculator h2 {
    color: #ff6b6b;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2rem;
    text-align: center;
}

.form-label {
    font-weight: 600;
    color: #4a4a4a;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 0.8rem;
    padding: 0.8rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #ff6b6b;
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 107, 0.25);
}

.btn-calculate {
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
    border: none;
    border-radius: 2rem;
    padding: 1rem 3rem;
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-calculate:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.bmi-results {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 2rem;
    margin-top: 2rem;
}

.bmi-value {
    font-size: 3.5rem;
    font-weight: 700;
    color: #ff6b6b;
    text-align: center;
    margin: 1rem 0;
}

.bmi-category {
    font-size: 1.5rem;
    font-weight: 600;
    color: #4a4a4a;
    text-align: center;
    margin-bottom: 2rem;
}

.bmi-scale {
    height: 2.5rem;
    background: linear-gradient(to right, #ff6b6b, #ffd93d, #6bff6b, #ff6b6b);
    border-radius: 1.5rem;
    position: relative;
    margin: 2rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.bmi-marker {
    position: absolute;
    top: -5px;
    width: 30px;
    height: 30px;
    background: white;
    border: 3px solid #ff6b6b;
    border-radius: 50%;
    transform: translateX(-50%);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.bmi-scale-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.bmi-recommendation {
    background: #fff5f5;
    border-left: 4px solid #ff6b6b;
    padding: 1.5rem;
    margin-top: 2rem;
    border-radius: 0.5rem;
}

.bmi-recommendation h4 {
    color: #ff6b6b;
    font-weight: 600;
    margin-bottom: 1rem;
}

.content-section {
    background: white;
    border-radius: 1.5rem;
    padding: 2.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.content-section h2 {
    color: #ff6b6b;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.content-section h3 {
    color: #4a4a4a;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 2rem 0 1rem;
}

.formula-box {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin: 1rem 0;
    text-align: center;
}

.formula-box p {
    font-size: 1.2rem;
    font-weight: 600;
    color: #ff6b6b;
    margin: 0;
}

.table {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.table thead th {
    background: #ff6b6b;
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
}

.sidebar {
    background: white;
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.sidebar h3 {
    color: #ff6b6b;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.list-group-item {
    border: none;
    border-radius: 0.8rem !important;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background: #fff5f5;
    transform: translateX(5px);
}

.list-group-item a {
    color: #4a4a4a;
    text-decoration: none;
    font-weight: 500;
}

/* 暗色模式适配 */
[data-theme="dark"] .section-header {
    background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
}

[data-theme="dark"] .bmi-calculator,
[data-theme="dark"] .content-section,
[data-theme="dark"] .sidebar {
    background: #2d3436;
    color: #f5f6fa;
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background: #353b48;
    border-color: #636e72;
    color: #f5f6fa;
}

[data-theme="dark"] .form-label {
    color: #f5f6fa;
}

[data-theme="dark"] .bmi-category {
    color: #f5f6fa;
}

[data-theme="dark"] .bmi-recommendation {
    background: #353b48;
}

[data-theme="dark"] .formula-box {
    background: #353b48;
}

[data-theme="dark"] .table {
    color: #f5f6fa;
}

[data-theme="dark"] .list-group-item {
    background: #353b48;
}

[data-theme="dark"] .list-group-item:hover {
    background: #2d3436;
}

[data-theme="dark"] .list-group-item a {
    color: #f5f6fa;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .section-header {
        padding: 3rem 0;
    }
    
    .section-header h1 {
        font-size: 2.2rem;
    }
    
    .bmi-calculator {
        padding: 1.5rem;
        margin-top: -2rem;
    }
    
    .bmi-value {
        font-size: 2.8rem;
    }
    
    .content-section {
        padding: 1.5rem;
    }
}

/* Main Styles for Калькулятор Дробей Website */

/* Calculator Styles */
.calculator-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

.calculator-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    text-align: center;
}

.fraction-input {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 1rem;
}

.numerator, .denominator {
    width: 60px;
    text-align: center;
}

.fraction-line {
    width: 100%;
    height: 2px;
    background-color: var(--text-color);
    margin: 5px 0;
}

.operation-select {
    margin: 0 15px;
}

.result-container {
    margin-top: 2rem;
    padding: 1rem;
    background-color: #f5f5f5;
    border-radius: 5px;
    border-left: 5px solid var(--primary-color);
}

.step-by-step {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f9f9f9;
    border-radius: 5px;
    border-left: 3px solid var(--secondary-color);
}

.step-title {
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

/* Tool Cards */
.tool-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    padding: 1.5rem;
    margin-bottom: 2rem;
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.tool-card-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.tool-card-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: var(--primary-color);
}

.tool-card-text {
    color: var(--light-text);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

/* Features Section */
.features-section {
    padding: 3rem 0;
}

.feature-box {
    text-align: center;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.feature-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: var(--primary-color);
}

.feature-text {
    color: var(--light-text);
}

/* Examples Section */
.examples-section {
    padding: 3rem 0;
    background-color: #f5f7fa;
}

.example-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.example-question {
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.example-steps {
    background-color: #f9f9f9;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.example-result {
    font-weight: 700;
    color: var(--success-color);
}

/* Testimonials */
.testimonial-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.testimonial-text {
    font-style: italic;
    margin-bottom: 1rem;
}

.testimonial-author {
    font-weight: 700;
    color: var(--primary-color);
}

/* FAQ Section */
.faq-section {
    padding: 3rem 0;
}

.faq-item {
    margin-bottom: 1.5rem;
}

.faq-question {
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.faq-answer {
    color: var(--light-text);
}

/* Footer */
footer {
    background-color: #2c3e50;
    color: white;
    padding: 3rem 0 2rem;
}

footer h5 {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
}

footer a {
    color: rgba(255, 255, 255, 0.8);
    transition: color 0.3s ease;
}

footer a:hover {
    color: white;
    text-decoration: none;
}

.social-icons a {
    font-size: 1.5rem;
    margin-right: 15px;
}

footer hr {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .calculator-card {
        padding: 1.5rem;
    }
}

@media (max-width: 767.98px) {
    .hero-section {
        padding: 3rem 0;
    }
    
    .hero-title {
        font-size: 1.75rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .calculator-title {
        font-size: 1.25rem;
    }
    
    .feature-icon {
        font-size: 2.5rem;
    }
    
    .feature-title {
        font-size: 1.1rem;
    }
}

/* Fraction Display */
.fraction-display {
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    margin: 0 5px;
}

.fraction-display .numerator, 
.fraction-display .denominator {
    display: block;
    text-align: center;
}

.mixed-number {
    display: inline-flex;
    align-items: center;
}

.mixed-number-whole {
    margin-right: 5px;
}

/* Additional utility classes */
.mb-5 {
    margin-bottom: 3rem;
}

.mt-5 {
    margin-top: 3rem;
}

.text-center {
    text-align: center;
}

.rounded {
    border-radius: 10px;
}

.shadow {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease forwards;
}

.delay-1 {
    animation-delay: 0.2s;
}

.delay-2 {
    animation-delay: 0.4s;
}

.delay-3 {
    animation-delay: 0.6s;
} 