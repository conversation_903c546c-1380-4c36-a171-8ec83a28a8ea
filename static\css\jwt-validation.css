/* JWT Validation CSS */

/* Global Colors */
:root {
    --primary-color: #4776e6;
    --secondary-color: #8e54e9;
    --accent-color: #5e72e4;
    --header-color: #ff6b6b;
    --payload-color: #48dbfb;
    --signature-color: #1dd1a1;
    --valid-color: #00b894;
    --invalid-color: #ff6b6b;
    --warning-color: #feca57;
    --dark-color: #2d3748;
    --light-color: #f8f9fa;
    --shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #3a59c7 0%, #6c36b3 100%);
    padding: 6rem 0;
    color: #fff;
    border-radius: 0 0 70px 70px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
}

.hero-section::before {
    content: '';
    position: absolute;
    width: 150%;
    height: 100%;
    background: rgba(255, 255, 255, 0.08);
    transform: rotate(-45deg);
    top: -50%;
    left: -25%;
    pointer-events: none;
}

.hero-section::after {
    content: '';
    position: absolute;
    width: 150%;
    height: 100%;
    background: rgba(255, 255, 255, 0.05);
    transform: rotate(45deg);
    bottom: -50%;
    right: -25%;
    pointer-events: none;
}

/* Floating elements to create depth */
.hero-section .container::before {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
    border-radius: 50%;
    top: 10%;
    left: 5%;
    animation: float 15s infinite ease-in-out;
}

.hero-section .container::after {
    content: '';
    position: absolute;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
    border-radius: 50%;
    bottom: 10%;
    right: 5%;
    animation: float 20s infinite ease-in-out reverse;
}

@keyframes float {
    0%, 100% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(20px, 10px);
    }
    50% {
        transform: translate(0, 20px);
    }
    75% {
        transform: translate(-10px, 10px);
    }
}

.hero-title {
    font-weight: 800;
    font-size: 3.5rem;
    margin-bottom: 1.8rem;
    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    animation: fadeInDown 1s ease-out;
    letter-spacing: -0.5px;
    line-height: 1.2;
    color: #ffffff;
}

.hero-subtitle {
    font-size: 1.5rem;
    opacity: 1;
    margin-bottom: 2.5rem;
    animation: fadeInUp 1s ease-out;
    font-weight: 400;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.text-highlight {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.25);
    padding: 0.1em 0.3em;
    border-radius: 8px;
    margin: 0 -0.1em;
    position: relative;
    display: inline-block;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.text-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.2) 100%);
    border-radius: 8px;
    z-index: -1;
    opacity: 0.7;
    animation: shimmer 2.5s infinite linear;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Validator Card */
.validator-card {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: var(--shadow);
    padding: 2.5rem;
    margin-bottom: 3rem;
    transition: all 0.3s ease;
    transform: translateY(0);
    animation: fadeIn 0.8s ease-out;
}

.validator-card:hover {
    box-shadow: var(--hover-shadow);
}

.validator-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1.8rem;
    color: #333;
    position: relative;
    padding-bottom: 0.8rem;
}

.validator-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

/* Form Controls */
#jwt-token-input,
#jwt-secret-input {
    font-family: 'Roboto Mono', monospace;
    font-size: 0.9rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    resize: none;
    border: 1px solid #e0e0e0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

#jwt-token-input:focus,
#jwt-secret-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(71, 118, 230, 0.25);
}

/* Validation Results */
.validation-results {
    animation: fadeIn 0.5s ease-out;
}

/* Check Icons */
.check-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.check-icon.valid {
    color: var(--valid-color);
    background-color: rgba(0, 184, 148, 0.1);
}

.check-icon.invalid {
    color: var(--invalid-color);
    background-color: rgba(255, 107, 107, 0.1);
}

.check-icon.warning {
    color: var(--warning-color);
    background-color: rgba(254, 202, 87, 0.1);
}

/* Check Details */
.check-details h5 {
    font-size: 1rem;
    font-weight: 600;
}

/* Token Claims Table */
.table-claims {
    font-size: 0.9rem;
}

.table-claims th {
    font-weight: 600;
    background-color: rgba(71, 118, 230, 0.05);
}

.claim-name {
    font-weight: 600;
    font-family: 'Roboto Mono', monospace;
}

.claim-value {
    font-family: 'Roboto Mono', monospace;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.claim-description {
    font-size: 0.85rem;
    color: #6c757d;
}

/* Timeline */
.timeline-container {
    position: relative;
    padding: 40px 0;
    margin: 20px 0;
}

.timeline-line {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 4px;
    background-color: #e9ecef;
    transform: translateY(-50%);
}

.timeline-point {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 120px;
    text-align: center;
    transform: translateX(-50%);
}

.timeline-marker {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: var(--light-color);
    border: 2px solid #e9ecef;
    margin-bottom: 10px;
    z-index: 1;
}

.timeline-point.active .timeline-marker {
    background-color: var(--valid-color);
    border-color: rgba(0, 184, 148, 0.3);
    box-shadow: 0 0 0 4px rgba(0, 184, 148, 0.2);
}

.timeline-point.invalid .timeline-marker {
    background-color: var(--invalid-color);
    border-color: rgba(255, 107, 107, 0.3);
    box-shadow: 0 0 0 4px rgba(255, 107, 107, 0.2);
}

.timeline-point.current .timeline-marker {
    width: 20px;
    height: 20px;
    background-color: var(--accent-color);
    border-color: rgba(94, 114, 228, 0.3);
    box-shadow: 0 0 0 4px rgba(94, 114, 228, 0.2);
}

.timeline-label {
    padding-top: 5px;
}

.timeline-label h5 {
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 3px;
}

.timeline-label p {
    font-size: 0.75rem;
    margin-bottom: 0;
    color: #6c757d;
}

#timeline-issued {
    left: 0%;
}

#timeline-nbf {
    left: 25%;
}

#timeline-now {
    left: 50%;
}

#timeline-exp {
    left: 100%;
}

/* JWT Structure Visual */
.jwt-structure-visual {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    margin: 20px 0;
}

.jwt-part {
    flex: 1;
    min-width: 150px;
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.jwt-part:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.jwt-part-label {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 10px;
    color: #495057;
}

.jwt-part-content {
    font-family: 'Roboto Mono', monospace;
    font-size: 0.85rem;
    word-break: break-all;
    color: #212529;
}

.jwt-dot {
    font-weight: bold;
    font-size: 24px;
    color: #495057;
    display: flex;
    align-items: center;
}

.header {
    background-color: rgba(255, 107, 107, 0.1);
    border-left: 4px solid var(--header-color);
}

.payload {
    background-color: rgba(72, 219, 251, 0.1);
    border-left: 4px solid var(--payload-color);
}

.signature {
    background-color: rgba(29, 209, 161, 0.1);
    border-left: 4px solid var(--signature-color);
}

/* Code Example Cards */
.code-example-card {
    margin-bottom: 1.5rem;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.code-example-card:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.code-example-card .card-header {
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.code-example-card .card-header h3 {
    font-size: 1.2rem;
    margin: 0;
}

.code-example-card .card-body {
    padding: 0;
}

.code-example-card pre {
    margin: 0;
    padding: 1.5rem;
    font-size: 0.9rem;
    line-height: 1.5;
    background-color: #f8f9fa;
    border-radius: 0;
    max-height: 350px;
    overflow-y: auto;
}

/* Resource Cards */
.resource-card {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    height: 100%;
    transition: all 0.3s ease;
}

.resource-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.resource-icon {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.resource-content h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.resource-content p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* Copy Button */
.copy-btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    transition: all 0.2s ease;
}

.copy-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.copy-btn.copied {
    background-color: var(--valid-color);
    color: white;
    border-color: var(--valid-color);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .hero-title {
        font-size: 2.8rem;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.2rem;
    }
    
    .hero-section {
        padding: 3rem 0;
    }
    
    .validator-card {
        padding: 1.5rem;
    }
    
    .jwt-structure-visual {
        flex-direction: column;
        align-items: stretch;
    }
    
    .timeline-point {
        width: 80px;
    }
    
    .timeline-label h5 {
        font-size: 0.75rem;
    }
    
    .timeline-label p {
        font-size: 0.7rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .validator-card,
    .card,
    .resource-card {
        background-color: #2d3748;
        color: #f8f9fa;
    }
    
    .validator-title,
    .card-header h4,
    .resource-content h3 {
        color: #f8f9fa;
    }
    
    #jwt-token-input,
    #jwt-secret-input {
        background-color: #1a202c;
        border-color: #4a5568;
        color: #f8f9fa;
    }
    
    .jwt-part {
        background-color: #2d3748;
    }
    
    .jwt-part-content,
    .jwt-part-label {
        color: #f8f9fa;
    }
    
    .text-muted,
    .timeline-label p,
    .claim-description {
        color: #a0aec0 !important;
    }
    
    .table-claims th {
        background-color: rgba(71, 118, 230, 0.15);
    }
    
    .table-claims,
    .table-claims td {
        color: #f8f9fa;
    }
} 