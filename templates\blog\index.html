{% extends "base.html" %}

{% block title %}JWT Decode Blog | Authentication & Security Resources{% endblock %}

{% block description %}Learn about JWT authentication, token security, and implementation techniques across different platforms. Expert tutorials and guides.{% endblock %}

{% block meta_keywords %}jwt blog, jwt security, jwt authentication, json web token, jwt tutorials{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/blog.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item active" aria-current="page">Blog</li>
    </ol>
</nav>

<section class="blog-index-header text-center mb-5">
    <h1 class="display-4">JWT Decode Blog</h1>
    <p class="lead">Authentication & Security Resources for Developers</p>
</section>

<div class="row">
    <div class="col-lg-8">
        <!-- Featured Article -->
        <div class="card mb-4">
            <div class="card-body">
                <span class="badge bg-primary mb-2">Featured</span>
                <h2 class="card-title h3">JWT Decode Guide: A Complete Guide for Developers</h2>
                <p class="card-text">Learn how to decode JWT tokens across different programming languages. Step-by-step guide with code examples and best practices for token handling.</p>
                <div class="mb-2 text-muted small">
                    <i class="far fa-calendar-alt me-1"></i> {{ format_date() }}
                    <i class="far fa-user ms-3 me-1"></i> JWT Decode Team
                    <i class="far fa-folder ms-3 me-1"></i> Development
                </div>
                <a href="/blog/jwt-decode-guide" class="btn btn-primary">Read Article</a>
            </div>
        </div>

        <!-- Recent Articles -->
        <h3 class="mb-4">Recent Articles</h3>
        
        <div class="card mb-4">
            <div class="card-body">
                <h3 class="card-title h4">JWT Decode in Different Programming Languages</h3>
                <p class="card-text">Comprehensive guide to implementing JWT decode functionality across various programming languages.</p>
                <div class="mb-2 text-muted small">
                    <i class="far fa-calendar-alt me-1"></i> March 26, 2025
                    <i class="far fa-user ms-3 me-1"></i> JWT Decode Team
                </div>
                <a href="/blog/jwt-decode-programming-languages" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-body">
                <h3 class="card-title h4">Understanding JWT in React Applications</h3>
                <p class="card-text">Learn how to implement JWT authentication in React apps for secure user sessions.</p>
                <div class="mb-2 text-muted small">
                    <i class="far fa-calendar-alt me-1"></i> March 15, 2025
                    <i class="far fa-user ms-3 me-1"></i> JWT Decode Team
                </div>
                <a href="/blog/jwt-in-react-applications" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-body">
                <h3 class="card-title h4">JWT vs Session Tokens: Which to Choose?</h3>
                <p class="card-text">A detailed comparison of JWT and session-based authentication approaches.</p>
                <div class="mb-2 text-muted small">
                    <i class="far fa-calendar-alt me-1"></i> March 10, 2025
                    <i class="far fa-user ms-3 me-1"></i> JWT Decode Team
                </div>
                <a href="/blog/jwt-vs-session-tokens" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-body">
                <h3 class="card-title h4">JWT Authentication in Flutter Apps</h3>
                <p class="card-text">Step-by-step guide to implementing JWT authentication in your Flutter applications.</p>
                <div class="mb-2 text-muted small">
                    <i class="far fa-calendar-alt me-1"></i> March 5, 2025
                    <i class="far fa-user ms-3 me-1"></i> JWT Decode Team
                </div>
                <a href="/blog/jwt-authentication-flutter" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-body">
                <h3 class="card-title h4">Command Line JWT Tools for Developers</h3>
                <p class="card-text">Boost your productivity with these powerful command-line tools for JWT management.</p>
                <div class="mb-2 text-muted small">
                    <i class="far fa-calendar-alt me-1"></i> March 1, 2025
                    <i class="far fa-user ms-3 me-1"></i> JWT Decode Team
                </div>
                <a href="/blog/command-line-jwt-tools" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
            </div>
        </div>
        
        <!-- Pagination -->
        <nav class="my-4">
            <ul class="pagination justify-content-center">
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
            </ul>
        </nav>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Categories -->
        <div class="card mb-4">
            <div class="card-header">Categories</div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <ul class="list-unstyled mb-0">
                            <li><a href="#" class="text-decoration-none">Authentication</a></li>
                            <li><a href="#" class="text-decoration-none">Security</a></li>
                            <li><a href="#" class="text-decoration-none">React</a></li>
                            <li><a href="#" class="text-decoration-none">Flutter</a></li>
                        </ul>
                    </div>
                    <div class="col-6">
                        <ul class="list-unstyled mb-0">
                            <li><a href="#" class="text-decoration-none">JavaScript</a></li>
                            <li><a href="#" class="text-decoration-none">Node.js</a></li>
                            <li><a href="#" class="text-decoration-none">Best Practices</a></li>
                            <li><a href="#" class="text-decoration-none">Tutorials</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Popular Posts -->
        <div class="card mb-4">
            <div class="card-header">Popular Posts</div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-3">
                        <a href="/blog/jwt-decode-guide" class="text-decoration-none">JWT Decode Guide: A Complete Guide for Developers</a>
                    </li>
                    <li class="mb-3">
                        <a href="/blog/jwt-decode-programming-languages" class="text-decoration-none">JWT Decode in Different Programming Languages</a>
                    </li>
                    <li class="mb-3">
                        <a href="/blog/jwt-in-react-applications" class="text-decoration-none">Understanding JWT in React Applications</a>
                    </li>
                    <li class="mb-3">
                        <a href="/blog/jwt-vs-session-tokens" class="text-decoration-none">JWT vs Session Tokens: Which to Choose?</a>
                    </li>
                    <li>
                        <a href="/blog/jwt-authentication-flutter" class="text-decoration-none">JWT Authentication in Flutter Apps</a>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Subscribe -->
        <div class="card mb-4">
            <div class="card-header">Subscribe to Our Newsletter</div>
            <div class="card-body">
                <p>Get the latest JWT security tips and updates delivered to your inbox.</p>
                <form>
                    <div class="mb-3">
                        <input class="form-control" type="email" placeholder="Email address" required>
                    </div>
                    <button class="btn btn-primary" type="submit">Subscribe</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %} 