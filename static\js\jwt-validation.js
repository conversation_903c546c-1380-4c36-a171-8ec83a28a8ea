/**
 * JWT Validation JavaScript
 * Client-side JWT token validation and verification
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const jwtInput = document.getElementById('jwt-token-input');
    const jwtSecret = document.getElementById('jwt-secret-input');
    const validateButton = document.getElementById('validate-button');
    const clearButton = document.getElementById('clear-button');
    const validationResults = document.getElementById('validation-results');
    const validationStatus = document.getElementById('validation-status');
    const structureCheck = document.getElementById('structure-check');
    const signatureCheck = document.getElementById('signature-check');
    const expiryCheck = document.getElementById('expiry-check');
    const nbfCheck = document.getElementById('nbf-check');
    const jwtHeaderContent = document.getElementById('jwt-header-content');
    const jwtPayloadContent = document.getElementById('jwt-payload-content');
    const jwtSignatureStatus = document.getElementById('jwt-signature-status');
    const claimsTableBody = document.getElementById('claims-table-body');
    const copyButtons = document.querySelectorAll('.copy-btn');
    
    // Validation option checkboxes
    const checkExp = document.getElementById('check-exp');
    const checkNbf = document.getElementById('check-nbf');
    const checkSignature = document.getElementById('check-signature');
    
    // Timeline elements
    const issuedDate = document.getElementById('issued-date');
    const nbfDate = document.getElementById('nbf-date');
    const currentDate = document.getElementById('current-date');
    const expDate = document.getElementById('exp-date');
    const timelineIssued = document.getElementById('timeline-issued');
    const timelineNbf = document.getElementById('timeline-nbf');
    const timelineExp = document.getElementById('timeline-exp');

    // Standard claims descriptions for the claims table
    const standardClaimsInfo = {
        iss: "Issuer - identifies the principal that issued the JWT",
        sub: "Subject - identifies the principal that is the subject of the JWT",
        aud: "Audience - identifies the recipients the JWT is intended for",
        exp: "Expiration Time - identifies the expiration time on or after which the JWT must not be accepted",
        nbf: "Not Before - identifies the time before which the JWT must not be accepted",
        iat: "Issued At - identifies the time at which the JWT was issued",
        jti: "JWT ID - provides a unique identifier for the JWT",
        name: "Full name of the user",
        email: "Email address of the user",
        roles: "User roles or permissions",
        scope: "Scope of access granted",
        azp: "Authorized party - the party to which the ID Token was issued",
        auth_time: "Time when the authentication occurred"
    };

    // Initialize tooltips if tippy.js is available
    if (typeof tippy === 'function') {
        tippy('[data-tippy-content]');
    }

    // Event Listeners
    validateButton.addEventListener('click', validateToken);
    clearButton.addEventListener('click', clearAll);
    
    // Ctrl+Enter shortcut for validation
    jwtInput.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            validateToken();
        }
    });
    
    // Copy button functionality
    copyButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetId = button.getAttribute('data-target');
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                copyToClipboard(targetElement.textContent);
                showCopyFeedback(button, true);
            }
        });
    });

    // Decode JWT token
    function decodeJWT(token) {
        try {
            token = token.trim();
            const parts = token.split('.');
            
            if (parts.length !== 3) {
                throw new Error('Invalid JWT format. Expected 3 parts separated by dots.');
            }
            
            const headerPart = parts[0];
            const payloadPart = parts[1];
            const signaturePart = parts[2];
            
            if (!headerPart || !payloadPart || !signaturePart) {
                throw new Error('Invalid JWT: All parts must be non-empty');
            }
            
            // Base64 URL decode function
            const base64UrlDecode = (str) => {
                let padded = str.replace(/-/g, '+').replace(/_/g, '/');
                const padding = padded.length % 4;
                if (padding) {
                    padded += '='.repeat(4 - padding);
                }
                try {
                    return JSON.parse(atob(padded));
                } catch (e) {
                    throw new Error(`Failed to parse JWT part: ${e.message}`);
                }
            };
            
            const header = base64UrlDecode(headerPart);
            const payload = base64UrlDecode(payloadPart);
            
            // Basic validation of decoded data
            if (!header || typeof header !== 'object') {
                throw new Error('Invalid JWT header: Must be a valid JSON object');
            }
            
            if (!payload || typeof payload !== 'object') {
                throw new Error('Invalid JWT payload: Must be a valid JSON object');
            }
            
            return {
                header,
                payload,
                signature: signaturePart,
                parts: { headerPart, payloadPart, signaturePart },
                valid: true,
                raw: token
            };
        } catch (error) {
            console.error('JWT decode error:', error);
            return {
                error: error.message,
                valid: false,
                raw: token || ''
            };
        }
    }

    // Verify JWT signature (HMAC signatures only for client-side validation)
    async function verifySignature(token, secret) {
        if (!secret || !token) {
            return { valid: false, reason: 'Missing token or secret key' };
        }
        
        try {
            const parts = token.split('.');
            const headerBase64 = parts[0];
            const payloadBase64 = parts[1];
            const signatureBase64 = parts[2];
            
            // Decode the header to check the algorithm
            const header = JSON.parse(atob(headerBase64.replace(/-/g, '+').replace(/_/g, '/')));
            
            // Only support HS256, HS384, and HS512 for client-side verification
            if (!header.alg || !header.alg.startsWith('HS')) {
                return { 
                    valid: false, 
                    reason: `Algorithm ${header.alg} not supported for client-side verification. Only HMAC algorithms (HS256, HS384, HS512) are supported.` 
                };
            }
            
            // Convert algorithm name to subtle crypto name
            const cryptoAlgName = {
                'HS256': 'SHA-256',
                'HS384': 'SHA-384',
                'HS512': 'SHA-512'
            }[header.alg];
            
            if (!cryptoAlgName) {
                return { valid: false, reason: `Unsupported algorithm: ${header.alg}` };
            }
            
            // The data to sign
            const encoder = new TextEncoder();
            const data = encoder.encode(parts[0] + '.' + parts[1]);
            const secretBuffer = encoder.encode(secret);
            
            // Import the key
            const key = await window.crypto.subtle.importKey(
                'raw',
                secretBuffer,
                {
                    name: 'HMAC',
                    hash: { name: cryptoAlgName }
                },
                false,
                ['verify']
            );
            
            // Convert base64url signature to binary
            const signatureString = atob(signatureBase64.replace(/-/g, '+').replace(/_/g, '/').replace(/=+$/, ''));
            const signatureArray = new Uint8Array(signatureString.length);
            for (let i = 0; i < signatureString.length; i++) {
                signatureArray[i] = signatureString.charCodeAt(i);
            }
            
            // Verify the signature
            const isValid = await window.crypto.subtle.verify(
                'HMAC',
                key,
                signatureArray,
                data
            );
            
            return { 
                valid: isValid, 
                reason: isValid ? 'Signature verified successfully' : 'Invalid signature' 
            };
        } catch (error) {
            console.error('Signature verification error:', error);
            return { valid: false, reason: `Verification error: ${error.message}` };
        }
    }

    // Validate token expiration
    function validateExpiration(payload) {
        if (!payload.exp) {
            return { valid: null, reason: 'No expiration claim found' };
        }
        
        const now = Math.floor(Date.now() / 1000);
        const exp = payload.exp;
        
        if (typeof exp !== 'number') {
            return { valid: false, reason: 'Invalid expiration format, expected number' };
        }
        
        const isValid = now < exp;
        const timeRemaining = exp - now;
        
        let reason;
        if (isValid) {
            if (timeRemaining < 3600) {
                reason = `Token expires soon (${Math.floor(timeRemaining / 60)} minutes remaining)`;
            } else {
                reason = `Token is valid (expires in ${Math.floor(timeRemaining / 3600)} hours)`;
            }
        } else {
            reason = `Token expired ${Math.floor((now - exp) / 3600)} hours ago`;
        }
        
        return { valid: isValid, reason, exp, now };
    }

    // Validate token not before claim
    function validateNotBefore(payload) {
        if (!payload.nbf) {
            return { valid: null, reason: 'No not-before claim found' };
        }
        
        const now = Math.floor(Date.now() / 1000);
        const nbf = payload.nbf;
        
        if (typeof nbf !== 'number') {
            return { valid: false, reason: 'Invalid not-before format, expected number' };
        }
        
        const isValid = now >= nbf;
        const waitTime = nbf - now;
        
        let reason;
        if (isValid) {
            reason = 'Token is active (not-before date has passed)';
        } else {
            reason = `Token not yet active (will be valid in ${Math.ceil(waitTime / 60)} minutes)`;
        }
        
        return { valid: isValid, reason, nbf, now };
    }

    // Format date from unix timestamp
    function formatDate(timestamp) {
        if (!timestamp) return '-';
        const date = new Date(timestamp * 1000);
        return date.toLocaleString();
    }

    // Update the timeline visualization
    function updateTimeline(payload, expResult, nbfResult) {
        const now = Math.floor(Date.now() / 1000);
        
        // Set current time
        currentDate.textContent = formatDate(now);
        
        // Set issued time if available
        if (payload.iat) {
            issuedDate.textContent = formatDate(payload.iat);
            const iatPosition = calculateTimelinePosition(payload.iat, payload.exp);
            timelineIssued.style.left = `${iatPosition}%`;
        }
        
        // Set not-before time if available
        if (payload.nbf) {
            nbfDate.textContent = formatDate(payload.nbf);
            const nbfPosition = calculateTimelinePosition(payload.nbf, payload.exp);
            timelineNbf.style.left = `${nbfPosition}%`;
            
            // Add active/inactive class
            if (nbfResult && nbfResult.valid === true) {
                timelineNbf.classList.add('active');
            } else if (nbfResult && nbfResult.valid === false) {
                timelineNbf.classList.add('invalid');
            }
        }
        
        // Set expiration time if available
        if (payload.exp) {
            expDate.textContent = formatDate(payload.exp);
            
            // Add expired/valid class
            if (expResult && expResult.valid === true) {
                timelineExp.classList.add('active');
            } else if (expResult && expResult.valid === false) {
                timelineExp.classList.add('invalid');
            }
        }
    }

    // Calculate position on timeline as percentage
    function calculateTimelinePosition(timestamp, expTimestamp) {
        if (!timestamp) return 0;
        
        const now = Math.floor(Date.now() / 1000);
        const totalDuration = (expTimestamp || now + 3600) - (now - 3600);
        const position = ((timestamp - (now - 3600)) / totalDuration) * 100;
        
        // Clamp position between 0 and 100
        return Math.max(0, Math.min(100, position));
    }

    // Populate claims table with payload data
    function populateClaimsTable(payload) {
        claimsTableBody.innerHTML = '';
        
        if (!payload) return;
        
        Object.entries(payload).forEach(([key, value]) => {
            const row = document.createElement('tr');
            
            // Claim name cell
            const nameCell = document.createElement('td');
            nameCell.className = 'claim-name';
            nameCell.textContent = key;
            row.appendChild(nameCell);
            
            // Claim value cell
            const valueCell = document.createElement('td');
            valueCell.className = 'claim-value';
            
            // Format date for time-related claims
            if (['exp', 'iat', 'nbf', 'auth_time'].includes(key) && typeof value === 'number') {
                const date = new Date(value * 1000);
                valueCell.textContent = value;
                valueCell.setAttribute('title', date.toLocaleString());
                
                // Add data attribute for sorting
                valueCell.setAttribute('data-datetime', value);
            } else if (typeof value === 'object') {
                valueCell.textContent = JSON.stringify(value);
            } else {
                valueCell.textContent = value;
            }
            
            row.appendChild(valueCell);
            
            // Claim description cell
            const descCell = document.createElement('td');
            descCell.className = 'claim-description';
            descCell.textContent = standardClaimsInfo[key] || '';
            row.appendChild(descCell);
            
            claimsTableBody.appendChild(row);
        });
    }

    // Update visual elements with validation status
    function updateValidationStatus(structureValid, signatureResult, expResult, nbfResult) {
        // Update overall status
        let overallStatus = 'Valid';
        let statusClass = 'bg-success';
        
        // Check structure
        if (!structureValid) {
            overallStatus = 'Invalid Structure';
            statusClass = 'bg-danger';
            structureCheck.innerHTML = '<i class="fas fa-times"></i>';
            structureCheck.classList.add('invalid');
        } else {
            structureCheck.innerHTML = '<i class="fas fa-check"></i>';
            structureCheck.classList.add('valid');
        }
        
        // Check signature if needed
        if (checkSignature.checked && signatureResult) {
            if (!signatureResult.valid) {
                if (statusClass !== 'bg-danger') {
                    overallStatus = 'Invalid Signature';
                    statusClass = 'bg-danger';
                }
                signatureCheck.innerHTML = '<i class="fas fa-times"></i>';
                signatureCheck.classList.add('invalid');
            } else {
                signatureCheck.innerHTML = '<i class="fas fa-check"></i>';
                signatureCheck.classList.add('valid');
            }
        } else if (checkSignature.checked) {
            signatureCheck.innerHTML = '<i class="fas fa-question"></i>';
            signatureCheck.classList.add('warning');
            
            if (statusClass !== 'bg-danger') {
                overallStatus = 'Unverified';
                statusClass = 'bg-warning text-dark';
            }
        } else {
            signatureCheck.innerHTML = '<i class="fas fa-minus"></i>';
            signatureCheck.classList.add('warning');
        }
        
        // Check expiration if needed
        if (checkExp.checked && expResult) {
            if (expResult.valid === false) {
                if (statusClass !== 'bg-danger') {
                    overallStatus = 'Expired';
                    statusClass = 'bg-danger';
                }
                expiryCheck.innerHTML = '<i class="fas fa-times"></i>';
                expiryCheck.classList.add('invalid');
            } else if (expResult.valid === true) {
                expiryCheck.innerHTML = '<i class="fas fa-check"></i>';
                expiryCheck.classList.add('valid');
            } else {
                expiryCheck.innerHTML = '<i class="fas fa-minus"></i>';
                expiryCheck.classList.add('warning');
            }
        } else {
            expiryCheck.innerHTML = '<i class="fas fa-minus"></i>';
            expiryCheck.classList.add('warning');
        }
        
        // Check nbf if needed
        if (checkNbf.checked && nbfResult) {
            if (nbfResult.valid === false) {
                if (statusClass !== 'bg-danger') {
                    overallStatus = 'Not Yet Active';
                    statusClass = 'bg-danger';
                }
                nbfCheck.innerHTML = '<i class="fas fa-times"></i>';
                nbfCheck.classList.add('invalid');
            } else if (nbfResult.valid === true) {
                nbfCheck.innerHTML = '<i class="fas fa-check"></i>';
                nbfCheck.classList.add('valid');
            } else {
                nbfCheck.innerHTML = '<i class="fas fa-minus"></i>';
                nbfCheck.classList.add('warning');
            }
        } else {
            nbfCheck.innerHTML = '<i class="fas fa-minus"></i>';
            nbfCheck.classList.add('warning');
        }
        
        // Update the badge
        validationStatus.textContent = overallStatus;
        validationStatus.className = `badge ${statusClass}`;
    }

    // Format JSON with syntax highlighting
    function formatJSON(obj) {
        if (!obj) return '';
        
        return JSON.stringify(obj, null, 4)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, match => {
                let cls = 'json-number';
                let color = '#007bff'; // Default blue for numbers
                
                if (/^"/.test(match)) {
                    if (/:$/.test(match)) {
                        cls = 'json-key';
                        color = '#e83e8c'; // Pink for keys
                    } else {
                        cls = 'json-string';
                        color = '#28a745'; // Green for strings
                    }
                } else if (/true|false/.test(match)) {
                    cls = 'json-boolean';
                    color = '#ff6b6b'; // Red for booleans
                } else if (/null/.test(match)) {
                    cls = 'json-null';
                    color = '#6c757d'; // Gray for null
                }
                
                return `<span class="${cls}" style="color: ${color}">${match}</span>`;
            });
    }

    // Copy text to clipboard
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).catch(err => {
            console.error('Could not copy text: ', err);
        });
    }

    // Show feedback on copy button
    function showCopyFeedback(button, success) {
        const originalText = button.innerHTML;
        if (success) {
            button.innerHTML = '<i class="fas fa-check"></i> Copied';
            button.classList.add('copied');
        } else {
            button.innerHTML = '<i class="fas fa-times"></i> Failed';
            button.classList.add('failed');
        }
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('copied', 'failed');
        }, 2000);
    }

    // Main validation function
    async function validateToken() {
        // Reset previous results
        resetValidationVisuals();
        
        const token = jwtInput.value.trim();
        if (!token) {
            alert('Please enter a JWT token');
            return;
        }
        
        // Decode the token
        const decoded = decodeJWT(token);
        
        if (!decoded.valid) {
            showValidationError(decoded.error || 'Invalid token format');
            return;
        }
        
        // Display the token structure
        displayTokenStructure(decoded);
        
        // Show validation results section
        validationResults.style.display = 'block';
        
        // Validate signature if requested and secret is provided
        let signatureResult = null;
        if (checkSignature.checked) {
            const secret = jwtSecret.value.trim();
            if (secret) {
                signatureResult = await verifySignature(token, secret);
                jwtSignatureStatus.textContent = signatureResult.reason;
            } else {
                jwtSignatureStatus.textContent = 'Secret key required for signature verification';
            }
        } else {
            jwtSignatureStatus.textContent = 'Signature verification skipped';
        }
        
        // Validate expiration
        let expResult = null;
        if (checkExp.checked && decoded.payload.exp) {
            expResult = validateExpiration(decoded.payload);
        }
        
        // Validate not-before
        let nbfResult = null;
        if (checkNbf.checked && decoded.payload.nbf) {
            nbfResult = validateNotBefore(decoded.payload);
        }
        
        // Populate claims table
        populateClaimsTable(decoded.payload);
        
        // Update timeline visualization
        updateTimeline(decoded.payload, expResult, nbfResult);
        
        // Update validation status visuals
        updateValidationStatus(decoded.valid, signatureResult, expResult, nbfResult);
    }

    // Reset validation visuals
    function resetValidationVisuals() {
        // Reset check icons
        structureCheck.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i>';
        signatureCheck.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i>';
        expiryCheck.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i>';
        nbfCheck.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i>';
        
        // Remove status classes
        structureCheck.className = 'check-icon me-2';
        signatureCheck.className = 'check-icon me-2';
        expiryCheck.className = 'check-icon me-2';
        nbfCheck.className = 'check-icon me-2';
        
        // Reset timeline classes
        timelineIssued.className = 'timeline-point';
        timelineNbf.className = 'timeline-point';
        timelineExp.className = 'timeline-point';
        
        // Reset timeline positions
        timelineIssued.style.left = '0%';
        timelineNbf.style.left = '25%';
        timelineExp.style.left = '100%';
        
        // Reset dates
        issuedDate.textContent = '-';
        nbfDate.textContent = '-';
        expDate.textContent = '-';
        currentDate.textContent = '-';
    }

    // Display token structure
    function displayTokenStructure(decoded) {
        jwtHeaderContent.innerHTML = formatJSON(decoded.header);
        jwtPayloadContent.innerHTML = formatJSON(decoded.payload);
    }

    // Show validation error
    function showValidationError(message) {
        validationResults.style.display = 'block';
        validationStatus.textContent = 'Invalid';
        validationStatus.className = 'badge bg-danger';
        
        structureCheck.innerHTML = '<i class="fas fa-times"></i>';
        structureCheck.classList.add('invalid');
        
        jwtHeaderContent.textContent = 'Invalid header';
        jwtPayloadContent.textContent = 'Invalid payload';
        jwtSignatureStatus.textContent = message;
    }

    // Clear all inputs and results
    function clearAll() {
        jwtInput.value = '';
        jwtSecret.value = '';
        validationResults.style.display = 'none';
        resetValidationVisuals();
    }

    // Check URL for token parameter and load it if present
    function checkUrlForToken() {
        const url = new URL(window.location.href);
        const token = url.searchParams.get('token');
        if (token) {
            jwtInput.value = token;
            validateToken();
        }
    }

    // Initialize page
    checkUrlForToken();
}); 