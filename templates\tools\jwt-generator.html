{% extends "base.html" %}

{% block title %}JWT Generator Online | Free JWT Token Generator Tool{% endblock %}

{% block description %}Generate JWT tokens instantly with our free online JWT generator. Support for multiple algorithms including HS256, RS256, ES256. Create tokens with custom payloads and secret keys.{% endblock %}

{% block meta_keywords %}jwt generator, jwt generator online, jwt token generator, jwt encode, jwt generator tool, es256 jwt generator, jwt secret key generator, jwt encode online{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
<link href="{{ url_for('static', filename='css/jwt-generator.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item">Tools</li>
        <li class="breadcrumb-item active" aria-current="page">JWT Generator</li>
    </ol>
</nav>

<!-- Hero Section -->
<section class="hero-section text-center py-4">
    <div class="container">
        <h1 class="hero-title mb-3"><span class="text-primary-light">JWT</span> <span class="text-highlight">Generator</span> <span class="text-primary-light">Online:</span> Create Custom Tokens</h1>
        <p class="hero-subtitle mb-4">Free, secure, and instant JWT token generation right in your browser</p>
    </div>
</section>

<!-- Main Generator Tool Section -->
<section id="generator-tool" class="py-4">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="generator-card">
                    <h2 class="generator-title">JWT Token Generator</h2>
                    
                    <div class="row">
                        <!-- Header Section -->
                        <div class="col-md-12 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="mb-0">Header</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="algorithm" class="form-label">Algorithm</label>
                                            <select id="algorithm" class="form-select">
                                                <option value="HS256" selected>HS256 (HMAC with SHA-256)</option>
                                                <option value="HS384">HS384 (HMAC with SHA-384)</option>
                                                <option value="HS512">HS512 (HMAC with SHA-512)</option>
                                                <option value="RS256">RS256 (RSA with SHA-256)</option>
                                                <option value="RS384">RS384 (RSA with SHA-384)</option>
                                                <option value="RS512">RS512 (RSA with SHA-512)</option>
                                                <option value="ES256">ES256 (ECDSA with SHA-256)</option>
                                                <option value="ES384">ES384 (ECDSA with SHA-384)</option>
                                                <option value="ES512">ES512 (ECDSA with SHA-512)</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="token-type" class="form-label">Token Type</label>
                                            <select id="token-type" class="form-select">
                                                <option value="JWT" selected>JWT</option>
                                            </select>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="add-custom-header">
                                                <label class="form-check-label" for="add-custom-header">Add Custom Header Fields</label>
                                            </div>
                                        </div>
                                        <div id="custom-header-section" class="col-md-12 mt-3" style="display: none;">
                                            <div class="custom-header-editor mb-3">
                                                <label for="custom-header" class="form-label">Custom Header JSON</label>
                                                <textarea id="custom-header" class="form-control" rows="4" placeholder='{"kid": "key-id-12345"}'></textarea>
                                                <div class="form-text">Enter custom header fields in JSON format</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payload Section -->
                        <div class="col-md-12 mb-4">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h3 class="mb-0">Payload (Claims)</h3>
                                    <div class="btn-group">
                                        <button id="add-standard-claims-btn" class="btn btn-sm btn-outline-primary">Add Standard Claims</button>
                                        <button id="clear-payload-btn" class="btn btn-sm btn-outline-secondary">Clear</button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="standard-claims-section mb-4">
                                        <div class="row">
                                            <div class="col-md-4 mb-3">
                                                <label for="iss-claim" class="form-label">Issuer (iss)</label>
                                                <input type="text" id="iss-claim" class="form-control" placeholder="https://your-domain.com">
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label for="sub-claim" class="form-label">Subject (sub)</label>
                                                <input type="text" id="sub-claim" class="form-control" placeholder="user123">
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label for="aud-claim" class="form-label">Audience (aud)</label>
                                                <input type="text" id="aud-claim" class="form-control" placeholder="your-client-id">
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label for="exp-claim" class="form-label">Expiration Time (exp)</label>
                                                <div class="input-group">
                                                    <input type="number" id="exp-value" class="form-control" value="1">
                                                    <select id="exp-unit" class="form-select">
                                                        <option value="hours">Hours</option>
                                                        <option value="days" selected>Days</option>
                                                        <option value="months">Months</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label for="nbf-claim" class="form-label">Not Before (nbf)</label>
                                                <div class="input-group">
                                                    <input type="number" id="nbf-value" class="form-control" value="0">
                                                    <select id="nbf-unit" class="form-select">
                                                        <option value="minutes" selected>Minutes</option>
                                                        <option value="hours">Hours</option>
                                                        <option value="days">Days</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label for="jti-claim" class="form-label">JWT ID (jti)</label>
                                                <div class="input-group">
                                                    <input type="text" id="jti-claim" class="form-control" placeholder="Unique identifier">
                                                    <button id="generate-jti-btn" class="btn btn-outline-primary">Generate</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="custom-claims-section">
                                        <label for="custom-payload" class="form-label">Custom Claims (JSON)</label>
                                        <textarea id="custom-payload" class="form-control" rows="8" placeholder='{"name": "John Doe", "role": "admin", "permissions": ["read", "write"]}'></textarea>
                                        <div class="form-text">Enter custom claims in JSON format</div>
                                    </div>
                                    
                                    <div class="live-preview mt-3">
                                        <label class="form-label">Combined Payload Preview</label>
                                        <pre id="payload-preview" class="json-preview rounded-3 p-3"></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Signature Section -->
                        <div class="col-md-12 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="mb-0">Signature</h3>
                                </div>
                                <div class="card-body">
                                    <div id="symmetric-key-section">
                                        <div class="mb-3">
                                            <label for="secret-key" class="form-label">Secret Key</label>
                                            <div class="input-group">
                                                <input type="text" id="secret-key" class="form-control" placeholder="Enter your secret key">
                                                <button id="generate-secret-btn" class="btn btn-outline-primary">Generate</button>
                                            </div>
                                            <div class="form-text">For HMAC algorithms (HS256, HS384, HS512)</div>
                                        </div>
                                    </div>
                                    
                                    <div id="asymmetric-key-section" style="display: none;">
                                        <div class="alert alert-info mb-3">
                                            <i class="fas fa-info-circle me-2"></i> For RSA and ECDSA algorithms, you need a private key to sign the token.
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="private-key" class="form-label">Private Key (PEM format)</label>
                                            <textarea id="private-key" class="form-control" rows="5" placeholder="-----BEGIN PRIVATE KEY-----
...
-----END PRIVATE KEY-----"></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="public-key" class="form-label">Public Key (For verification)</label>
                                            <textarea id="public-key" class="form-control" rows="5" placeholder="-----BEGIN PUBLIC KEY-----
...
-----END PUBLIC KEY-----"></textarea>
                                        </div>
                                        
                                        <div class="text-end">
                                            <button id="generate-keypair-btn" class="btn btn-outline-primary">Generate Key Pair</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Generate Button Section -->
                        <div class="col-md-12 text-center mb-4">
                            <button id="generate-token-btn" class="btn btn-lg btn-primary animate__animated animate__pulse animate__infinite animate__slow">
                                <i class="fas fa-cog me-2"></i>Generate JWT Token
                            </button>
                        </div>
                        
                        <!-- Result Section -->
                        <div class="col-md-12 mb-4">
                            <div id="result-section" class="card" style="display: none;">
                                <div class="card-header">
                                    <h3 class="mb-0">Generated JWT Token</h3>
                                </div>
                                <div class="card-body">
                                    <div class="token-visualization mb-4">
                                        <div class="jwt-part header">
                                            <div class="jwt-part-label">HEADER</div>
                                            <div id="header-part" class="jwt-part-content"></div>
                                        </div>
                                        <div class="jwt-dot">.</div>
                                        <div class="jwt-part payload">
                                            <div class="jwt-part-label">PAYLOAD</div>
                                            <div id="payload-part" class="jwt-part-content"></div>
                                        </div>
                                        <div class="jwt-dot">.</div>
                                        <div class="jwt-part signature">
                                            <div class="jwt-part-label">SIGNATURE</div>
                                            <div id="signature-part" class="jwt-part-content">HMACSHA256(...)</div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="generated-token" class="form-label">JWT Token</label>
                                        <div class="input-group">
                                            <textarea id="generated-token" class="form-control" rows="4" readonly></textarea>
                                            <button id="copy-token-btn" class="btn btn-outline-primary" data-tippy-content="Copy token to clipboard">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="token-info mt-3">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <div class="token-info-item">
                                                    <span class="token-info-label">Algorithm:</span>
                                                    <span id="token-algorithm-info" class="token-info-value"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <div class="token-info-item">
                                                    <span class="token-info-label">Issued At:</span>
                                                    <span id="token-iat-info" class="token-info-value"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <div class="token-info-item">
                                                    <span class="token-info-label">Expires:</span>
                                                    <span id="token-exp-info" class="token-info-value"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <div class="token-info-item">
                                                    <span class="token-info-label">Token Size:</span>
                                                    <span id="token-size-info" class="token-info-value"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="token-actions mt-3 text-end">
                                        <a id="download-token-btn" href="#" class="btn btn-sm btn-outline-primary me-2">
                                            <i class="fas fa-download me-1"></i> Download
                                        </a>
                                        <a id="verify-token-btn" href="/tools/jwt-validation" class="btn btn-sm btn-outline-primary me-2">
                                            <i class="fas fa-check-circle me-1"></i> Verify
                                        </a>
                                        <a id="decode-token-btn" href="/" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-key me-1"></i> Decode
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- How it Works Section -->
<section class="how-it-works py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">How JWT Generation Works</h2>
        
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="steps-card">
                    <h3 class="mb-4"><i class="fas fa-list-ol me-2 text-primary"></i>Steps to Generate a JWT</h3>
                    <ol class="generation-steps">
                        <li>
                            <strong>Create the Header</strong>
                            <p>The header typically consists of the token type and the signing algorithm being used.</p>
                            <pre class="code-example">{"alg": "HS256", "typ": "JWT"}</pre>
                        </li>
                        <li>
                            <strong>Create the Payload</strong>
                            <p>The payload contains claims (statements about an entity) and additional data.</p>
                            <pre class="code-example">{"sub": "1234567890", "name": "John Doe", "iat": 1516239022}</pre>
                        </li>
                        <li>
                            <strong>Create the Signature</strong>
                            <p>The signature is created by encoding the header and payload, and then signing with the secret key.</p>
                            <pre class="code-example">HMACSHA256(
  base64UrlEncode(header) + "." +
  base64UrlEncode(payload),
  secret)</pre>
                        </li>
                        <li>
                            <strong>Put it All Together</strong>
                            <p>Combine the encoded header, payload, and signature with dots (.) between them.</p>
                            <pre class="code-example">xxxxx.yyyyy.zzzzz</pre>
                        </li>
                    </ol>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="jwt-info-card mb-4">
                    <h3 class="mb-4"><i class="fas fa-key me-2 text-primary"></i>JWT Algorithms</h3>
                    <div class="algorithm-types">
                        <div class="algorithm-type mb-3">
                            <h4>HMAC (Symmetric)</h4>
                            <p>Uses a single secret key for both signing and verification.</p>
                            <ul>
                                <li><strong>HS256</strong> - HMAC with SHA-256</li>
                                <li><strong>HS384</strong> - HMAC with SHA-384</li>
                                <li><strong>HS512</strong> - HMAC with SHA-512</li>
                            </ul>
                        </div>
                        
                        <div class="algorithm-type mb-3">
                            <h4>RSA (Asymmetric)</h4>
                            <p>Uses a private key for signing and a public key for verification.</p>
                            <ul>
                                <li><strong>RS256</strong> - RSA with SHA-256</li>
                                <li><strong>RS384</strong> - RSA with SHA-384</li>
                                <li><strong>RS512</strong> - RSA with SHA-512</li>
                            </ul>
                        </div>
                        
                        <div class="algorithm-type">
                            <h4>ECDSA (Asymmetric)</h4>
                            <p>Uses Elliptic Curve cryptography for improved security with smaller key sizes.</p>
                            <ul>
                                <li><strong>ES256</strong> - ECDSA with P-256 and SHA-256</li>
                                <li><strong>ES384</strong> - ECDSA with P-384 and SHA-384</li>
                                <li><strong>ES512</strong> - ECDSA with P-521 and SHA-512</li>
                            </ul>
                        </div>
                    </div>
                </div>
                

            </div>
        </div>
    </div>
</section>

<!-- Common Use Cases Section -->
<section class="use-cases-section py-5">
    <div class="container">
        <h2 class="text-center mb-5">Common JWT Use Cases</h2>
        
        <div class="row">
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-sign-in-alt"></i>
                    </div>
                    <h3>Authentication</h3>
                    <p>Securely transmit user identity information after login to maintain sessions.</p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-user-lock"></i>
                    </div>
                    <h3>Authorization</h3>
                    <p>Grant specific permissions and access control using claims in the token payload.</p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <h3>API Authentication</h3>
                    <p>Secure API endpoints with tokens to validate client requests without sessions.</p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <i class="fas fa-share-alt"></i>
                    </div>
                    <h3>Information Exchange</h3>
                    <p>Securely share information between parties with tamper-proof guarantee.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Key Pair Generation Modal -->
<div class="modal fade" id="keypairModal" tabindex="-1" aria-labelledby="keypairModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="keypairModalLabel">Generate Key Pair</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="key-type" class="form-label">Key Type</label>
                    <select id="key-type" class="form-select">
                        <option value="RSA" selected>RSA</option>
                        <option value="EC">EC (Elliptic Curve)</option>
                    </select>
                </div>
                
                <div id="rsa-options">
                    <div class="mb-3">
                        <label for="rsa-key-size" class="form-label">Key Size</label>
                        <select id="rsa-key-size" class="form-select">
                            <option value="2048" selected>2048 bits</option>
                            <option value="3072">3072 bits</option>
                            <option value="4096">4096 bits</option>
                        </select>
                    </div>
                </div>
                
                <div id="ec-options" style="display: none;">
                    <div class="mb-3">
                        <label for="ec-curve" class="form-label">Curve</label>
                        <select id="ec-curve" class="form-select">
                            <option value="P-256" selected>P-256</option>
                            <option value="P-384">P-384</option>
                            <option value="P-521">P-521</option>
                        </select>
                    </div>
                </div>
                
                <div class="progress mb-3" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button id="confirm-generate-keypair" type="button" class="btn btn-primary">Generate</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jsrsasign/10.5.24/jsrsasign-all-min.js"></script>
<script src="{{ url_for('static', filename='js/jwt-generator.js') }}"></script>
{% endblock %} 