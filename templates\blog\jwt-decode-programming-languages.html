{% extends "base.html" %}

{% block title %}How to Decode JWT Tokens in 5 Different Programming Languages | JWT Decode Online{% endblock %}

{% block description %}Learn how to decode JWT tokens in JavaScript, Python, Java, C#, and Flutter. Comprehensive guide with code examples for each language.{% endblock %}

{% block meta_keywords %}jwt decode, jwt decode online, jwt decode javascript, jwt decode python, jwt decode java, jwt decode c#, jwt decode flutter, jwt token, jwt-decode npm, jwt decode command line{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/blog.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/blog">Blog</a></li>
        <li class="breadcrumb-item active" aria-current="page">How to Decode JWT Tokens in 5 Different Programming Languages</li>
    </ol>
</nav>

<article class="blog-post">
    <header class="blog-post-header">
        <h1 class="blog-post-title">How to Decode JWT Tokens in 5 Different Programming Languages</h1>
        <div class="blog-post-meta">
            <span class="post-date"><i class="far fa-calendar-alt me-1"></i> Published: {{ format_date() }}</span>
            <span class="post-author"><i class="far fa-user me-1"></i> By: JWT Decode Team</span>
            <span class="post-category"><i class="far fa-folder me-1"></i> Category: Development</span>
        </div>
    </header>

    <div class="blog-post-content">
        <div class="blog-post-intro">
            <p class="lead">JSON Web Tokens (JWT) have become the standard for authentication in modern applications. Whether you're working with JavaScript, Python, Java, C#, or Flutter, understanding how to decode JWT tokens is essential for implementing secure authentication. This comprehensive guide will show you how to decode JWT tokens in five popular programming languages, with practical code examples and best practices.</p>
        </div>

        <div class="table-of-contents card my-4">
            <div class="card-header">
                <h2 class="mb-0 h5">Table of Contents</h2>
            </div>
            <div class="card-body">
                <ul>
                    <li><a href="#understanding-jwt">Understanding JWT Structure</a></li>
                    <li><a href="#javascript">JWT Decode in JavaScript</a></li>
                    <li><a href="#python">JWT Decode in Python</a></li>
                    <li><a href="#java">JWT Decode in Java</a></li>
                    <li><a href="#csharp">JWT Decode in C#</a></li>
                    <li><a href="#flutter">JWT Decode in Flutter</a></li>
                    <li><a href="#best-practices">Best Practices and Security Considerations</a></li>
                    <li><a href="#common-issues">Common Issues and Troubleshooting</a></li>
                </ul>
            </div>
        </div>

        <section id="understanding-jwt">
            <h2>Understanding JWT Structure</h2>
            <p>Before diving into language-specific implementations, let's understand the basic structure of a JWT token. A JWT consists of three parts separated by dots (.):</p>
            
            <div class="jwt-structure-visual my-4">
                <div class="jwt-part header">
                    <div class="jwt-part-label">HEADER</div>
                    <div class="jwt-part-content">{"alg": "HS256", "typ": "JWT"}</div>
                </div>
                <div class="jwt-dot">.</div>
                <div class="jwt-part payload">
                    <div class="jwt-part-label">PAYLOAD</div>
                    <div class="jwt-part-content">{"sub": "1234567890", "name": "John Doe"}</div>
                </div>
                <div class="jwt-dot">.</div>
                <div class="jwt-part signature">
                    <div class="jwt-part-label">SIGNATURE</div>
                    <div class="jwt-part-content">HMACSHA256(base64UrlEncode(header) + "." + base64UrlEncode(payload), secret)</div>
                </div>
            </div>
        </section>

        <section id="javascript">
            <h2>JWT Decode in JavaScript</h2>
            <p>JavaScript offers several ways to decode JWT tokens. The most popular approach is using the <code>jwt-decode</code> npm package:</p>
            
            <div class="code-block">
                <pre><code class="language-javascript">// Using npm package
import jwtDecode from 'jwt-decode';

// Decode token
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
const decoded = jwtDecode(token);

// Access payload data
console.log(decoded.sub);  // User ID
console.log(decoded.name); // User name</code></pre>
            </div>

            <p>For React applications, you can use the same package:</p>
            
            <div class="code-block">
                <pre><code class="language-javascript">// In React component
import { useEffect, useState } from 'react';
import jwtDecode from 'jwt-decode';

function UserProfile() {
    const [userData, setUserData] = useState(null);

    useEffect(() => {
        const token = localStorage.getItem('token');
        if (token) {
            const decoded = jwtDecode(token);
            setUserData(decoded);
        }
    }, []);

    return (
        // Component JSX
    );
}</code></pre>
            </div>
        </section>

        <section id="python">
            <h2>JWT Decode in Python</h2>
            <p>Python developers can use the <code>PyJWT</code> library to decode JWT tokens:</p>
            
            <div class="code-block">
                <pre><code class="language-python">import jwt

# Decode token without verification
token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
decoded = jwt.decode(token, options={"verify_signature": False})

# Decode and verify token
secret = 'your-secret-key'
decoded = jwt.decode(token, secret, algorithms=["HS256"])

# Access payload data
print(decoded['sub'])  # User ID
print(decoded['name']) # User name</code></pre>
            </div>

            <p>For Flask applications, you can integrate JWT decoding with your authentication system:</p>
            
            <div class="code-block">
                <pre><code class="language-python">from flask import request, jsonify
import jwt

@app.route('/api/protected')
def protected_route():
    token = request.headers.get('Authorization')
    if not token:
        return jsonify({'error': 'Token missing'}), 401
    
    try:
        decoded = jwt.decode(token, app.config['SECRET_KEY'], algorithms=["HS256"])
        return jsonify({'user': decoded})
    except jwt.InvalidTokenError:
        return jsonify({'error': 'Invalid token'}), 401</code></pre>
            </div>
        </section>

        <section id="java">
            <h2>JWT Decode in Java</h2>
            <p>Java developers can use the <code>jjwt</code> library to decode JWT tokens:</p>
            
            <div class="code-block">
                <pre><code class="language-java">import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

// Decode token
String token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
String secret = "your-secret-key";

Claims claims = Jwts.parserBuilder()
    .setSigningKey(secret.getBytes())
    .build()
    .parseClaimsJws(token)
    .getBody();

// Access payload data
String userId = claims.getSubject();
String name = claims.get("name", String.class);</code></pre>
            </div>

            <p>For Spring Boot applications, you can use the built-in JWT support:</p>
            
            <div class="code-block">
                <pre><code class="language-java">@RestController
@RequestMapping("/api")
public class JwtController {
    
    @GetMapping("/user")
    public ResponseEntity<?> getUserInfo(@RequestHeader("Authorization") String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                .setSigningKey(secretKey.getBytes())
                .build()
                .parseClaimsJws(token)
                .getBody();
            
            return ResponseEntity.ok(claims);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }
    }
}</code></pre>
            </div>
        </section>

        <section id="csharp">
            <h2>JWT Decode in C#</h2>
            <p>C# developers can use the <code>System.IdentityModel.Tokens.Jwt</code> package to decode JWT tokens:</p>
            
            <div class="code-block">
                <pre><code class="language-csharp">using System.IdentityModel.Tokens.Jwt;
using Microsoft.IdentityModel.Tokens;

// Decode token
string token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
var handler = new JwtSecurityTokenHandler();
var jsonToken = handler.ReadToken(token) as JwtSecurityToken;

// Access payload data
string userId = jsonToken.Claims.First(claim => claim.Type == "sub").Value;
string name = jsonToken.Claims.First(claim => claim.Type == "name").Value;</code></pre>
            </div>

            <p>For ASP.NET Core applications, you can use the built-in JWT authentication:</p>
            
            <div class="code-block">
                <pre><code class="language-csharp">[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    [HttpGet("user")]
    public IActionResult GetUserInfo()
    {
        var token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
        var handler = new JwtSecurityTokenHandler();
        var jsonToken = handler.ReadToken(token) as JwtSecurityToken;
        
        return Ok(new {
            UserId = jsonToken.Claims.First(c => c.Type == "sub").Value,
            Name = jsonToken.Claims.First(c => c.Type == "name").Value
        });
    }
}</code></pre>
            </div>
        </section>

        <section id="flutter">
            <h2>JWT Decode in Flutter</h2>
            <p>Flutter developers can use the <code>jwt_decoder</code> package to decode JWT tokens:</p>
            
            <div class="code-block">
                <pre><code class="language-dart">import 'package:jwt_decoder/jwt_decoder.dart';

// Decode token
String token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
Map<String, dynamic> decodedToken = JwtDecoder.decode(token);

// Access payload data
String userId = decodedToken['sub'];
String name = decodedToken['name'];

// Check if token is expired
bool isExpired = JwtDecoder.isExpired(token);</code></pre>
            </div>

            <p>For Flutter applications with state management:</p>
            
            <div class="code-block">
                <pre><code class="language-dart">class AuthService {
  Future<UserModel> getUserFromToken(String token) async {
    try {
      Map<String, dynamic> decodedToken = JwtDecoder.decode(token);
      
      return UserModel(
        id: decodedToken['sub'],
        name: decodedToken['name'],
        email: decodedToken['email'],
      );
    } catch (e) {
      throw Exception('Invalid token');
    }
  }
}</code></pre>
            </div>
        </section>

        <section id="best-practices">
            <h2>Best Practices and Security Considerations</h2>
            <ul>
                <li>Always verify token signatures when decoding in production environments</li>
                <li>Check token expiration before processing</li>
                <li>Validate token claims against your application's requirements</li>
                <li>Use secure storage methods for tokens (e.g., HttpOnly cookies, secure storage)</li>
                <li>Implement proper error handling for invalid tokens</li>
                <li>Keep your JWT libraries updated to the latest versions</li>
                <li>Use appropriate token expiration times</li>
                <li>Implement token refresh mechanisms for long-lived sessions</li>
            </ul>
        </section>

        <section id="common-issues">
            <h2>Common Issues and Troubleshooting</h2>
            <h3>1. Invalid Token Format</h3>
            <p>Ensure your token follows the correct JWT format (header.payload.signature). Common issues include:</p>
            <ul>
                <li>Missing or extra dots in the token</li>
                <li>Invalid base64url encoding</li>
                <li>Malformed JSON in header or payload</li>
            </ul>

            <h3>2. Signature Verification Failures</h3>
            <p>Common causes of signature verification failures:</p>
            <ul>
                <li>Incorrect secret key</li>
                <li>Mismatched algorithm in token header</li>
                <li>Token tampering</li>
            </ul>

            <h3>3. Token Expiration</h3>
            <p>Handle token expiration gracefully:</p>
            <ul>
                <li>Implement proper error handling for expired tokens</li>
                <li>Use refresh tokens for seamless user experience</li>
                <li>Set appropriate expiration times based on your security requirements</li>
            </ul>
        </section>

        <div class="related-posts mt-5">
            <h3>Related Articles</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Understanding JWT in React Applications</h5>
                            <p class="card-text">Learn how to implement JWT authentication in React apps for secure user sessions.</p>
                            <a href="/blog/jwt-in-react-applications" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">JWT Authentication in Flutter Apps</h5>
                            <p class="card-text">Step-by-step guide to implementing JWT authentication in your Flutter applications.</p>
                            <a href="/blog/jwt-authentication-flutter" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Command Line JWT Tools for Developers</h5>
                            <p class="card-text">Boost your productivity with these powerful command-line tools for JWT management.</p>
                            <a href="/blog/command-line-jwt-tools" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</article>
{% endblock %} 