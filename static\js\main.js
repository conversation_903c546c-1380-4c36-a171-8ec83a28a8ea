/**
 * Calculadora de Horas - Main JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化手风琴
    var accordionItems = document.querySelectorAll('.faq-question');
    if (accordionItems.length > 0) {
        accordionItems.forEach(function(item) {
            item.addEventListener('click', function() {
                var answer = this.nextElementSibling;
                var icon = this.querySelector('i');
                
                if (answer.style.display === 'block') {
                    answer.style.display = 'none';
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                } else {
                    answer.style.display = 'block';
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                }
            });
        });
    }

    // 初始化轮播图
    var testimonialCarousel = document.getElementById('testimonialCarousel');
    if (testimonialCarousel) {
        new bootstrap.Carousel(testimonialCarousel, {
            interval: 5000,
            wrap: true
        });
    }

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // 暗色/亮色模式切换
    const themeToggle = document.getElementById('theme-toggle');
    const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');
    
    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        document.documentElement.setAttribute('data-theme', savedTheme);
    } else if (prefersDarkScheme.matches) {
        document.documentElement.setAttribute('data-theme', 'dark');
    }
    
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        });
    }

    // Ascendente Calculator Form
    const calculatorForm = document.getElementById('ascendente-calculator-form');
    const resultDiv = document.getElementById('result');
    const locationInput = document.getElementById('location');
    const getLocationBtn = document.getElementById('get-location');
    
    if (getLocationBtn) {
        getLocationBtn.addEventListener('click', function() {
            if (!navigator.geolocation) {
                showError('Il tuo browser non supporta la geolocalizzazione.');
                return;
            }

            getLocationBtn.disabled = true;
            getLocationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Ricerca posizione...';

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const latitude = position.coords.latitude;
                    const longitude = position.coords.longitude;
                    
                    // 使用反向地理编码获取城市名称
                    fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}`)
                        .then(response => response.json())
                        .then(data => {
                            locationInput.value = data.display_name;
                            getLocationBtn.disabled = false;
                            getLocationBtn.innerHTML = '<i class="fas fa-location-dot"></i> Usa la mia posizione';
                        })
                        .catch(error => {
                            showError('Errore nel recupero del nome della città.');
                            getLocationBtn.disabled = false;
                            getLocationBtn.innerHTML = '<i class="fas fa-location-dot"></i> Usa la mia posizione';
                        });
                },
                function(error) {
                    let errorMessage = 'Errore nel recupero della posizione.';
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMessage = 'Permesso di accesso alla posizione negato.';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMessage = 'Informazioni sulla posizione non disponibili.';
                            break;
                        case error.TIMEOUT:
                            errorMessage = 'Richiesta di posizione scaduta.';
                            break;
                        default:
                            errorMessage = 'Errore sconosciuto.';
                            break;
                    }
                    showError(errorMessage);
                    getLocationBtn.disabled = false;
                    getLocationBtn.innerHTML = '<i class="fas fa-location-dot"></i> Usa la mia posizione';
                }
            );
        });
    }
    
    if (calculatorForm) {
        calculatorForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const birthDate = document.getElementById('birth-date').value;
            const birthTime = document.getElementById('birth-time').value;
            const location = locationInput.value;
            
            // Validate form
            if (!birthDate || !birthTime || !location) {
                showError('Per favore, compila tutti i campi richiesti.');
                return;
            }
            
            // Show loading state
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="alert alert-info">Calcolo in corso...</div>';
            
            // Simulate API call (replace with actual API call)
            setTimeout(() => {
                // Mock result data
                const result = {
                    ascendente: 'Ariete',
                    luna: 'Toro',
                    sole: 'Gemelli',
                    pianeti: [
                        { nome: 'Sole', segno: 'Gemelli', grado: 15 },
                        { nome: 'Luna', segno: 'Toro', grado: 8 },
                        { nome: 'Mercurio', segno: 'Gemelli', grado: 20 },
                        { nome: 'Venere', segno: 'Cancro', grado: 5 },
                        { nome: 'Marte', segno: 'Leone', grado: 12 },
                        { nome: 'Giove', segno: 'Bilancia', grado: 25 },
                        { nome: 'Saturno', segno: 'Capricorno', grado: 10 }
                    ],
                    case: [
                        { nome: 'Casa 1', segno: 'Ariete', grado: 5 },
                        { nome: 'Casa 2', segno: 'Toro', grado: 2 },
                        { nome: 'Casa 3', segno: 'Gemelli', grado: 1 },
                        { nome: 'Casa 4', segno: 'Cancro', grado: 3 },
                        { nome: 'Casa 5', segno: 'Leone', grado: 5 },
                        { nome: 'Casa 6', segno: 'Vergine', grado: 7 }
                    ],
                    descrizione: 'Il tuo ascendente in Ariete indica una personalità forte e determinata. Sei una persona diretta, energica e spesso impulsiva. Tendi a prendere l\'iniziativa e ad affrontare le sfide con coraggio.'
                };
                
                // Generate HTML for the result
                let resultHTML = `
                    <div class="result-container">
                        <h3 class="text-center mb-4">Il tuo Tema Natale</h3>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header bg-primary text-white">
                                        <h4 class="mb-0">Informazioni Principali</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between mb-3">
                                            <div class="text-center">
                                                <div class="fs-1 mb-2"><i class="fas fa-sun"></i></div>
                                                <h5>Sole</h5>
                                                <p class="mb-0">${result.sole}</p>
                                            </div>
                                            <div class="text-center">
                                                <div class="fs-1 mb-2"><i class="fas fa-moon"></i></div>
                                                <h5>Luna</h5>
                                                <p class="mb-0">${result.luna}</p>
                                            </div>
                                            <div class="text-center">
                                                <div class="fs-1 mb-2"><i class="fas fa-arrow-up"></i></div>
                                                <h5>Ascendente</h5>
                                                <p class="mb-0">${result.ascendente}</p>
                                            </div>
                                        </div>
                                        <p class="mt-3">${result.descrizione}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header bg-primary text-white">
                                        <h4 class="mb-0">Grafico Zodiacale</h4>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="zodiacChart" width="400" height="400"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header bg-primary text-white">
                                        <h4 class="mb-0">Posizioni Planetarie</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Pianeta</th>
                                                        <th>Segno</th>
                                                        <th>Grado</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    ${result.pianeti.map(pianeta => `
                                                        <tr>
                                                            <td>${pianeta.nome}</td>
                                                            <td>${pianeta.segno}</td>
                                                            <td>${pianeta.grado}°</td>
                                                        </tr>
                                                    `).join('')}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header bg-primary text-white">
                                        <h4 class="mb-0">Case Astrologiche</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Casa</th>
                                                        <th>Segno</th>
                                                        <th>Grado</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    ${result.case.map(casa => `
                                                        <tr>
                                                            <td>${casa.nome}</td>
                                                            <td>${casa.segno}</td>
                                                            <td>${casa.grado}°</td>
                                                        </tr>
                                                    `).join('')}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                // Display result
                resultDiv.innerHTML = resultHTML;
                
                // Create zodiac chart
                createZodiacChart();
            }, 1500);
        });
    }
    
    // Create zodiac chart using Chart.js
    function createZodiacChart() {
        const ctx = document.getElementById('zodiacChart').getContext('2d');
        
        // Zodiac signs data
        const zodiacSigns = [
            'Ariete', 'Toro', 'Gemelli', 'Cancro', 
            'Leone', 'Vergine', 'Bilancia', 'Scorpione', 
            'Sagittario', 'Capricorno', 'Acquario', 'Pesci'
        ];
        
        // Random data for demonstration
        const data = zodiacSigns.map(() => Math.floor(Math.random() * 30) + 10);
        
        // Colors for each zodiac sign
        const colors = [
            '#FF5733', '#C70039', '#900C3F', '#581845',
            '#FFC300', '#DAF7A6', '#FF5733', '#C70039',
            '#900C3F', '#581845', '#FFC300', '#DAF7A6'
        ];
        
        // Create chart
        new Chart(ctx, {
            type: 'polarArea',
            data: {
                labels: zodiacSigns,
                datasets: [{
                    data: data,
                    backgroundColor: colors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: 'Distribuzione Zodiacale'
                    }
                }
            }
        });
    }

    // City Autocomplete
    const cityInput = document.getElementById('birth-city');
    
    if (cityInput) {
        cityInput.addEventListener('input', debounce(function(e) {
            const query = e.target.value;
            if (query.length < 2) return;
            
            // Simulate API call (replace with actual API call)
            fetch(`/api/cities?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    // Handle city suggestions
                    console.log('City suggestions:', data);
                })
                .catch(error => {
                    console.error('Error fetching cities:', error);
                });
        }, 300));
    }

    // 下拉子菜单交互
    const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
    
    dropdownSubmenus.forEach(submenu => {
        submenu.addEventListener('mouseenter', function() {
            const dropdownMenu = this.querySelector('.dropdown-menu');
            if (dropdownMenu) {
                dropdownMenu.classList.add('show');
            }
        });
        
        submenu.addEventListener('mouseleave', function() {
            const dropdownMenu = this.querySelector('.dropdown-menu');
            if (dropdownMenu) {
                dropdownMenu.classList.remove('show');
            }
        });
        
        const dropdownToggle = submenu.querySelector('.dropdown-toggle');
        if (dropdownToggle) {
            dropdownToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const dropdownMenu = this.nextElementSibling;
                if (dropdownMenu) {
                    dropdownMenu.classList.toggle('show');
                }
            });
        }
    });
    
    // 初始化BMI计算器
    initBmiCalculator();

    // 添加导航栏滚动效果
    const navbar = document.querySelector('.navbar');
    
    // 监听滚动事件
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
    
    // 确保当前页面的导航链接显示为激活状态
    const currentLocation = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar .nav-link');
    
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentLocation) {
            link.classList.add('active');
        } else if (currentLocation.includes(link.getAttribute('href')) && link.getAttribute('href') !== '/') {
            link.classList.add('active');
        }
    });

    // Copy result button functionality
    const copyResultButton = document.getElementById('copy-result-button');
    if (copyResultButton) {
        copyResultButton.addEventListener('click', function() {
            const resultContainer = document.getElementById('result-container');
            const fractionDisplay = resultContainer.querySelector('.fraction-display');
            
            if (fractionDisplay) {
                const numerator = fractionDisplay.querySelector('.numerator').textContent;
                const denominator = fractionDisplay.querySelector('.denominator').textContent;
                const resultText = `${numerator}/${denominator}`;
                
                navigator.clipboard.writeText(resultText)
                    .then(() => {
                        const originalText = copyResultButton.innerHTML;
                        copyResultButton.innerHTML = '<i class="fas fa-check"></i> Скопировано';
                        
                        setTimeout(() => {
                            copyResultButton.innerHTML = originalText;
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Ошибка при копировании: ', err);
                    });
            }
        });
    }
});

// BMI计算器初始化
function initBmiCalculator() {
    const bmiCalculator = document.getElementById('bmi-calculator');
    if (!bmiCalculator) return;
    
    const genderOptions = document.querySelectorAll('.gender-option');
    const calculateBtn = document.getElementById('calculate-bmi');
    const bmiResults = document.getElementById('bmi-results');
    const bmiValue = document.getElementById('bmi-value');
    const bmiCategory = document.getElementById('bmi-category');
    const bmiMarker = document.getElementById('bmi-marker');
    const bmiRecommendation = document.getElementById('bmi-recommendation');
    const bmiRecommendationText = document.getElementById('bmi-recommendation-text');
    
    // 性别选择
    genderOptions.forEach(option => {
        option.addEventListener('click', function() {
            genderOptions.forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            document.getElementById('gender').value = this.dataset.gender;
        });
    });
    
    // 计算BMI
    calculateBtn.addEventListener('click', function(e) {
        e.preventDefault();
        
        const gender = document.getElementById('gender').value;
        const age = parseFloat(document.getElementById('age').value);
        const weight = parseFloat(document.getElementById('weight').value);
        const height = parseFloat(document.getElementById('height').value) / 100; // 转换为米
        
        if (!gender || isNaN(age) || isNaN(weight) || isNaN(height)) {
            alert('Proszę wypełnić wszystkie pola formularza.');
            return;
        }
        
        if (age <= 0 || weight <= 0 || height <= 0) {
            alert('Wartości muszą być większe od zera.');
            return;
        }
        
        // 计算BMI
        const bmi = weight / (height * height);
        
        // 显示结果
        bmiValue.textContent = bmi.toFixed(1);
        
        // 设置BMI分类和推荐
        let category = '';
        let recommendation = '';
        let markerPosition = 0;
        
        if (bmi < 16) {
            category = 'Wygłodzenie';
            recommendation = 'Twoje BMI wskazuje na poważne wygłodzenie. Należy natychmiast skonsultować się z lekarzem.';
            markerPosition = 5;
        } else if (bmi < 17) {
            category = 'Wychudzenie';
            recommendation = 'Twoje BMI wskazuje na wychudzenie. Zalecana jest konsultacja z lekarzem lub dietetykiem w celu zwiększenia masy ciała.';
            markerPosition = 10;
        } else if (bmi < 18.5) {
            category = 'Niedowaga';
            recommendation = 'Masz niedowagę. Zalecamy zbilansowaną dietę bogatą w składniki odżywcze, aby osiągnąć zdrową masę ciała.';
            markerPosition = 20;
        } else if (bmi < 25) {
            category = 'Waga prawidłowa';
            recommendation = 'Gratulacje! Twoja waga jest w normie. Kontynuuj zdrowy styl życia, regularne ćwiczenia i zbilansowaną dietę.';
            markerPosition = 40;
        } else if (bmi < 30) {
            category = 'Nadwaga';
            recommendation = 'Masz nadwagę. Zalecamy umiarkowane zwiększenie aktywności fizycznej i zwrócenie uwagi na dietę.';
            markerPosition = 60;
        } else if (bmi < 35) {
            category = 'Otyłość I stopnia';
            recommendation = 'Twoje BMI wskazuje na otyłość I stopnia. Zalecamy konsultację z lekarzem lub dietetykiem w celu opracowania planu redukcji masy ciała.';
            markerPosition = 75;
        } else if (bmi < 40) {
            category = 'Otyłość II stopnia';
            recommendation = 'Twoje BMI wskazuje na otyłość II stopnia. Konieczna jest konsultacja medyczna i opracowanie planu redukcji masy ciała.';
            markerPosition = 85;
        } else {
            category = 'Otyłość III stopnia';
            recommendation = 'Twoje BMI wskazuje na otyłość III stopnia (otyłość olbrzymią). Konieczna jest niezwłoczna konsultacja medyczna.';
            markerPosition = 95;
        }
        
        bmiCategory.textContent = category;
        bmiRecommendationText.textContent = recommendation;
        bmiMarker.style.left = `${markerPosition}%`;
        
        // 显示结果区域
        bmiResults.classList.add('visible');
        
        // 平滑滚动到结果
        bmiResults.scrollIntoView({ behavior: 'smooth' });
    });
}

// 确保响应式导航栏正常工作
document.addEventListener('DOMContentLoaded', function() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    if (navbarToggler && navbarCollapse) {
        navbarToggler.addEventListener('click', function() {
            navbarCollapse.classList.toggle('show');
        });
    }
});

// Error handling
function showError(message) {
    const resultDiv = document.getElementById('result');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = `<div class="alert alert-danger">${message}</div>`;
}

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Global utility functions for fraction operations
const Fraction = {
    // Find greatest common divisor of two numbers
    gcd: function(a, b) {
        a = Math.abs(a);
        b = Math.abs(b);
        
        while (b) {
            const temp = b;
            b = a % b;
            a = temp;
        }
        
        return a;
    },
    
    // Find least common multiple of two numbers
    lcm: function(a, b) {
        return Math.abs(a * b) / this.gcd(a, b);
    },
    
    // Simplify a fraction
    simplify: function(numerator, denominator) {
        if (denominator === 0) {
            throw new Error("Знаменатель не может быть равен нулю");
        }
        
        // Make sure we're working with integers
        numerator = parseInt(numerator);
        denominator = parseInt(denominator);
        
        const gcd = this.gcd(numerator, denominator);
        
        return {
            numerator: numerator / gcd,
            denominator: denominator / gcd
        };
    },
    
    // Add two fractions
    add: function(num1, den1, num2, den2) {
        const lcm = this.lcm(den1, den2);
        const num1Scaled = num1 * (lcm / den1);
        const num2Scaled = num2 * (lcm / den2);
        const sumNumerator = num1Scaled + num2Scaled;
        
        return this.simplify(sumNumerator, lcm);
    },
    
    // Subtract two fractions
    subtract: function(num1, den1, num2, den2) {
        const lcm = this.lcm(den1, den2);
        const num1Scaled = num1 * (lcm / den1);
        const num2Scaled = num2 * (lcm / den2);
        const diffNumerator = num1Scaled - num2Scaled;
        
        return this.simplify(diffNumerator, lcm);
    },
    
    // Multiply two fractions
    multiply: function(num1, den1, num2, den2) {
        const resultNumerator = num1 * num2;
        const resultDenominator = den1 * den2;
        
        return this.simplify(resultNumerator, resultDenominator);
    },
    
    // Divide two fractions
    divide: function(num1, den1, num2, den2) {
        if (num2 === 0) {
            throw new Error("Нельзя делить на ноль");
        }
        
        // Division is the same as multiplying by the reciprocal
        const resultNumerator = num1 * den2;
        const resultDenominator = den1 * num2;
        
        return this.simplify(resultNumerator, resultDenominator);
    },
    
    // Convert fraction to decimal
    toDecimal: function(numerator, denominator) {
        return numerator / denominator;
    },
    
    // Convert fraction to mixed number
    toMixedNumber: function(numerator, denominator) {
        if (Math.abs(numerator) < Math.abs(denominator)) {
            return {
                whole: 0,
                numerator: numerator,
                denominator: denominator
            };
        }
        
        const whole = Math.floor(Math.abs(numerator) / denominator) * Math.sign(numerator);
        const remainderNumerator = Math.abs(numerator) % denominator;
        
        return {
            whole: whole,
            numerator: remainderNumerator,
            denominator: denominator
        };
    },
    
    // Format fraction for display
    formatFraction: function(numerator, denominator) {
        // Check if it's a whole number
        if (denominator === 1) {
            return numerator.toString();
        }
        
        // Display as a fraction
        return `${numerator}/${denominator}`;
    },
    
    // Format mixed number for display
    formatMixedNumber: function(whole, numerator, denominator) {
        if (whole === 0) {
            return this.formatFraction(numerator, denominator);
        }
        
        if (numerator === 0) {
            return whole.toString();
        }
        
        return `${whole} ${numerator}/${denominator}`;
    }
};

// Main calculator functions
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the basic calculator if it exists on the page
    const basicCalculator = document.getElementById('basic-fraction-calculator');
    if (basicCalculator) {
        initBasicCalculator();
    }
    
    // Initialize any other specialized calculators on the page
    // For example:
    const mixedNumberCalculator = document.getElementById('mixed-number-calculator');
    if (mixedNumberCalculator) {
        initMixedNumberCalculator();
    }
    
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const target = document.querySelector(targetId);
            if (target) {
                window.scrollTo({
                    top: target.offsetTop - 80,
                    behavior: 'smooth'
                });
            }
        });
    });
});

// Initialize the basic fraction calculator
function initBasicCalculator() {
    const calculateBtn = document.getElementById('calculate-button');
    const resultContainer = document.getElementById('result-container');
    const stepsContainer = document.getElementById('steps-container');
    
    if (!calculateBtn) return;
    
    calculateBtn.addEventListener('click', function() {
        // Get input values
        const num1 = parseInt(document.getElementById('numerator1').value) || 0;
        const den1 = parseInt(document.getElementById('denominator1').value) || 1;
        const num2 = parseInt(document.getElementById('numerator2').value) || 0;
        const den2 = parseInt(document.getElementById('denominator2').value) || 1;
        const operation = document.getElementById('operation').value;
        
        // Validate denominators cannot be zero
        if (den1 === 0 || den2 === 0) {
            resultContainer.innerHTML = '<div class="alert alert-danger">Ошибка: Знаменатель не может быть равен нулю</div>';
            stepsContainer.innerHTML = '';
            resultContainer.style.display = 'block';
            stepsContainer.style.display = 'none';
            return;
        }
        
        try {
            // Perform the calculation based on the operation
            let result;
            let steps = '';
            
            switch (operation) {
                case '+':
                    result = Fraction.add(num1, den1, num2, den2);
                    steps = generateAdditionSteps(num1, den1, num2, den2, result);
                    break;
                case '-':
                    result = Fraction.subtract(num1, den1, num2, den2);
                    steps = generateSubtractionSteps(num1, den1, num2, den2, result);
                    break;
                case '*':
                    result = Fraction.multiply(num1, den1, num2, den2);
                    steps = generateMultiplicationSteps(num1, den1, num2, den2, result);
                    break;
                case '/':
                    if (num2 === 0) {
                        throw new Error("Нельзя делить на ноль");
                    }
                    result = Fraction.divide(num1, den1, num2, den2);
                    steps = generateDivisionSteps(num1, den1, num2, den2, result);
                    break;
                default:
                    throw new Error("Неизвестная операция");
            }
            
            // Display the result in the new structure
            const numeratorSpan = resultContainer.querySelector('.numerator');
            const denominatorSpan = resultContainer.querySelector('.denominator');
            
            if (numeratorSpan && denominatorSpan) {
                numeratorSpan.textContent = result.numerator;
                denominatorSpan.textContent = result.denominator;
            } else {
                // Fallback for older structure
                resultContainer.innerHTML = `
                    <h3>Результат:</h3>
                    <div class="fraction-display">
                        <span class="numerator">${result.numerator}</span>
                        <div class="fraction-line"></div>
                        <span class="denominator">${result.denominator}</span>
                    </div>
                `;
            }
            
            // Display the step-by-step solution
            const stepContent = stepsContainer.querySelector('.step-content');
            if (stepContent) {
                stepContent.innerHTML = steps;
            } else {
                // Fallback for older structure
                stepsContainer.innerHTML = `
                    <h4 class="step-title">Решение по шагам:</h4>
                    <div class="step-by-step">
                        ${steps}
                    </div>
                `;
            }
            
            // Make containers visible
            resultContainer.style.display = 'block';
            stepsContainer.style.display = 'block';
            
            // Render math formulas
            if (typeof MathJax !== 'undefined') {
                MathJax.typeset([stepsContainer]);
            }
            
            // Enable copy button
            const copyResultButton = document.getElementById('copy-result-button');
            if (copyResultButton) {
                copyResultButton.disabled = false;
            }
        } catch (error) {
            resultContainer.innerHTML = `<div class="alert alert-danger">Ошибка: ${error.message}</div>`;
            stepsContainer.innerHTML = '';
            resultContainer.style.display = 'block';
            stepsContainer.style.display = 'none';
        }
    });
    
    // Clear button functionality
    const clearButton = document.getElementById('clear-button');
    if (clearButton) {
        clearButton.addEventListener('click', function() {
            document.getElementById('numerator1').value = '';
            document.getElementById('denominator1').value = '';
            document.getElementById('numerator2').value = '';
            document.getElementById('denominator2').value = '';
            document.getElementById('operation').value = '+';
            
            resultContainer.style.display = 'none';
            stepsContainer.style.display = 'none';
            
            const copyResultButton = document.getElementById('copy-result-button');
            if (copyResultButton) {
                copyResultButton.disabled = true;
            }
        });
    }
}

// Generate step-by-step explanation for addition
function generateAdditionSteps(num1, den1, num2, den2, result) {
    let steps = '';
    
    // Check if the denominators are the same
    if (den1 === den2) {
        steps += `
            <p>Шаг 1: Знаменатели одинаковые (${den1}), поэтому мы просто складываем числители:</p>
            <p>${num1} + ${num2} = ${num1 + num2}</p>
        `;
    } else {
        // Find the LCM of the denominators
        const lcm = Fraction.lcm(den1, den2);
        const factor1 = lcm / den1;
        const factor2 = lcm / den2;
        const scaledNum1 = num1 * factor1;
        const scaledNum2 = num2 * factor2;
        
        steps += `
            <p>Шаг 1: Находим наименьшее общее кратное (НОК) знаменателей ${den1} и ${den2}:</p>
            <p>НОК(${den1}, ${den2}) = ${lcm}</p>
            
            <p>Шаг 2: Преобразуем дроби к общему знаменателю:</p>
            <p>\\(\\frac{${num1}}{${den1}} = \\frac{${num1} \\times ${factor1}}{${den1} \\times ${factor1}} = \\frac{${scaledNum1}}{${lcm}}\\)</p>
            <p>\\(\\frac{${num2}}{${den2}} = \\frac{${num2} \\times ${factor2}}{${den2} \\times ${factor2}} = \\frac{${scaledNum2}}{${lcm}}\\)</p>
            
            <p>Шаг 3: Складываем числители:</p>
            <p>\\(\\frac{${scaledNum1}}{${lcm}} + \\frac{${scaledNum2}}{${lcm}} = \\frac{${scaledNum1} + ${scaledNum2}}{${lcm}} = \\frac{${scaledNum1 + scaledNum2}}{${lcm}}\\)</p>
        `;
    }
    
    // Check if the result needs to be simplified
    const gcd = Fraction.gcd(result.numerator * result.denominator, result.denominator * result.denominator);
    if (gcd > 1) {
        steps += `
            <p>Шаг 4: Сокращаем дробь, разделив числитель и знаменатель на их наибольший общий делитель (${gcd}):</p>
            <p>\\(\\frac{${result.numerator * gcd}}{${result.denominator * gcd}} = \\frac{${result.numerator}}{${result.denominator}}\\)</p>
        `;
    }
    
    return steps;
}

// Generate step-by-step explanation for subtraction
function generateSubtractionSteps(num1, den1, num2, den2, result) {
    let steps = '';
    
    // Check if the denominators are the same
    if (den1 === den2) {
        steps += `
            <p>Шаг 1: Знаменатели одинаковые (${den1}), поэтому мы просто вычитаем числители:</p>
            <p>${num1} - ${num2} = ${num1 - num2}</p>
        `;
    } else {
        // Find the LCM of the denominators
        const lcm = Fraction.lcm(den1, den2);
        const factor1 = lcm / den1;
        const factor2 = lcm / den2;
        const scaledNum1 = num1 * factor1;
        const scaledNum2 = num2 * factor2;
        
        steps += `
            <p>Шаг 1: Находим наименьшее общее кратное (НОК) знаменателей ${den1} и ${den2}:</p>
            <p>НОК(${den1}, ${den2}) = ${lcm}</p>
            
            <p>Шаг 2: Преобразуем дроби к общему знаменателю:</p>
            <p>\\(\\frac{${num1}}{${den1}} = \\frac{${num1} \\times ${factor1}}{${den1} \\times ${factor1}} = \\frac{${scaledNum1}}{${lcm}}\\)</p>
            <p>\\(\\frac{${num2}}{${den2}} = \\frac{${num2} \\times ${factor2}}{${den2} \\times ${factor2}} = \\frac{${scaledNum2}}{${lcm}}\\)</p>
            
            <p>Шаг 3: Вычитаем числители:</p>
            <p>\\(\\frac{${scaledNum1}}{${lcm}} - \\frac{${scaledNum2}}{${lcm}} = \\frac{${scaledNum1} - ${scaledNum2}}{${lcm}} = \\frac{${scaledNum1 - scaledNum2}}{${lcm}}\\)</p>
        `;
    }
    
    // Check if the result needs to be simplified
    const unsimplifiedNum = (den1 === den2) ? num1 - num2 : (num1 * (Fraction.lcm(den1, den2) / den1)) - (num2 * (Fraction.lcm(den1, den2) / den2));
    const unsimplifiedDen = (den1 === den2) ? den1 : Fraction.lcm(den1, den2);
    const gcd = Fraction.gcd(Math.abs(unsimplifiedNum), unsimplifiedDen);
    
    if (gcd > 1) {
        steps += `
            <p>Шаг 4: Сокращаем дробь, разделив числитель и знаменатель на их наибольший общий делитель (${gcd}):</p>
            <p>\\(\\frac{${unsimplifiedNum}}{${unsimplifiedDen}} = \\frac{${unsimplifiedNum / gcd}}{${unsimplifiedDen / gcd}}\\)</p>
        `;
    }
    
    return steps;
}

// Generate step-by-step explanation for multiplication
function generateMultiplicationSteps(num1, den1, num2, den2, result) {
    let steps = '';
    
    steps += `
        <p>Шаг 1: Умножаем числители и знаменатели:</p>
        <p>Числитель: ${num1} × ${num2} = ${num1 * num2}</p>
        <p>Знаменатель: ${den1} × ${den2} = ${den1 * den2}</p>
        <p>\\(\\frac{${num1}}{${den1}} \\times \\frac{${num2}}{${den2}} = \\frac{${num1} \\times ${num2}}{${den1} \\times ${den2}} = \\frac{${num1 * num2}}{${den1 * den2}}\\)</p>
    `;
    
    // Check if the result needs to be simplified
    const gcd = Fraction.gcd(num1 * num2, den1 * den2);
    if (gcd > 1) {
        steps += `
            <p>Шаг 2: Сокращаем дробь, разделив числитель и знаменатель на их наибольший общий делитель (${gcd}):</p>
            <p>\\(\\frac{${num1 * num2}}{${den1 * den2}} = \\frac{${(num1 * num2) / gcd}}{${(den1 * den2) / gcd}}\\)</p>
        `;
    }
    
    return steps;
}

// Generate step-by-step explanation for division
function generateDivisionSteps(num1, den1, num2, den2, result) {
    let steps = '';
    
    steps += `
        <p>Шаг 1: Для деления дробей, мы умножаем первую дробь на обратную вторую дробь:</p>
        <p>\\(\\frac{${num1}}{${den1}} \\div \\frac{${num2}}{${den2}} = \\frac{${num1}}{${den1}} \\times \\frac{${den2}}{${num2}}\\)</p>
        
        <p>Шаг 2: Умножаем числители и знаменатели:</p>
        <p>Числитель: ${num1} × ${den2} = ${num1 * den2}</p>
        <p>Знаменатель: ${den1} × ${num2} = ${den1 * num2}</p>
        <p>\\(\\frac{${num1}}{${den1}} \\times \\frac{${den2}}{${num2}} = \\frac{${num1} \\times ${den2}}{${den1} \\times ${num2}} = \\frac{${num1 * den2}}{${den1 * num2}}\\)</p>
    `;
    
    // Check if the result needs to be simplified
    const gcd = Fraction.gcd(num1 * den2, den1 * num2);
    if (gcd > 1) {
        steps += `
            <p>Шаг 3: Сокращаем дробь, разделив числитель и знаменатель на их наибольший общий делитель (${gcd}):</p>
            <p>\\(\\frac{${num1 * den2}}{${den1 * num2}} = \\frac{${(num1 * den2) / gcd}}{${(den1 * num2) / gcd}}\\)</p>
        `;
    }
    
    return steps;
}

// Initialize the mixed number calculator
function initMixedNumberCalculator() {
    // Implementation to be added when needed
    console.log("Mixed number calculator initialized");
}

// Add class to body to indicate JavaScript is loaded
document.body.classList.add('js-enabled'); 