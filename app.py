import os
from datetime import date, datetime

from flask import Flask, render_template
from flask_babel import Babel
from flask_frozen import Freezer

app = Flask(__name__)
babel = Babel(app)
freezer = Freezer(app)


@app.context_processor
def utility_processor():
    def format_date(date_obj=None):
        if date_obj is None:
            date_obj = datetime.now()
        return date_obj.strftime('%d/%m/%Y')

    return dict(format_date=format_date)


def template_exists(template_path):
    """检查模板文件是否存在"""
    full_path = os.path.join(app.template_folder, template_path)
    return os.path.exists(full_path)


@app.route('/')
def index():
    return render_template('index.html')


@app.route('/how-it-works')
def how_it_works():
    return render_template('how-it-works.html')


@app.route('/about')
def about():
    return render_template('about.html')


@app.route('/contact')
def contact():
    return render_template('contact.html')


@app.route('/privacy-policy')
def privacy_policy():
    return render_template('privacy-policy.html')


@app.route('/terms')
def terms():
    return render_template('terms.html')


@app.route('/tools/jwt-decode-offline')
def jwt_decode_offline():
    return render_template('tools/jwt-decode-offline.html')


@app.route('/tools/jwt-validation')
def jwt_validation():
    return render_template('tools/jwt-validation.html')


@app.route('/tools/jwt-generator')
def jwt_generator():
    return render_template('tools/jwt-generator.html')


@app.route('/blog/jwt-in-react-applications')
def jwt_in_react():
    return render_template('blog/jwt-in-react-applications.html')


@app.route('/blog/jwt-authentication-flutter')
def jwt_in_flutter():
    return render_template('blog/jwt-authentication-flutter.html')


@app.route('/blog/jwt-vs-session-tokens')
def jwt_vs_session():
    return render_template('blog/jwt-vs-session-tokens.html')


@app.route('/blog/command-line-jwt-tools')
def command_line_jwt_tools():
    return render_template('blog/command-line-jwt-tools.html')


@app.route('/blog/jwt-decode-guide')
def jwt_decode_guide():
    return render_template('blog/jwt-decode-guide.html')


@app.route('/blog/jwt-decode-programming-languages')
def jwt_decode_programming_languages():
    return render_template('blog/jwt-decode-programming-languages.html')


@app.route('/blog')
def blog():
    return render_template('blog/index.html')


@app.errorhandler(404)
def page_not_found(e):
    return render_template('404.html'), 404


if __name__ == '__main__':
    app.run(debug=True)
