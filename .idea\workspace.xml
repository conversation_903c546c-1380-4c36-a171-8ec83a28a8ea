<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b177c77c-df1e-4fe4-9653-dfec08d45ab2" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app.py" beforeDir="false" afterPath="$PROJECT_DIR$/app.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build.py" beforeDir="false" afterPath="$PROJECT_DIR$/build.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/config/tools.py" beforeDir="false" afterPath="$PROJECT_DIR$/config/tools.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/readme" beforeDir="false" afterPath="$PROJECT_DIR$/readme" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/static/favicon.ico" beforeDir="false" afterPath="$PROJECT_DIR$/static/favicon.ico" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/static/favicon.svg" beforeDir="false" afterPath="$PROJECT_DIR$/static/favicon.svg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/age-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/arbeitszeit-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/brutto-netto-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/calorie-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/compound-interest-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/cycle-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/date-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/datum-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/dreisatz-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/eisprung-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/fraction-calculator.js" beforeDir="false" afterPath="$PROJECT_DIR$/static/js/fraction-calculator.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/gewerbesteuer-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/graphic-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/hours-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/iban-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/imu-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/iva-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/kalorien-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/kalorienbedarf-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/kfz-steuer-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/kredit-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/metabolismo-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/mwst-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/nullstellen-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/ovulation-calculator.js" beforeDir="false" afterPath="$PROJECT_DIR$/static/js/ovulation-calculator.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/parcella-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/paypal-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/pedaggio-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/pension-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/pq-formel-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/pregnancy-calculator.js" beforeDir="false" afterPath="$PROJECT_DIR$/static/js/pregnancy-calculator.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/promille-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/proportion-calculator.js" beforeDir="false" afterPath="$PROJECT_DIR$/static/js/proportion-calculator.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/prozent-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/pythagoras-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/ral-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/renten-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/salary-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/schwangerschaft-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/scientific-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/sconto-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/spritverbrauch-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/ssw-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/stammfunktion-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/steuer-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/tfr-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/wissenschaftlich-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/working-days-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/zinseszins-calculator.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/robots.txt" beforeDir="false" afterPath="$PROJECT_DIR$/static/robots.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/static/sitemap.xml" beforeDir="false" afterPath="$PROJECT_DIR$/static/sitemap.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/altri-strumenti/data.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/altri-strumenti/eta.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/altri-strumenti/giorni-lavorativi.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/altri-strumenti/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/altri-strumenti/ore.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/altri-strumenti/parcella.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/altri-strumenti/pedaggio.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/base.html" beforeDir="false" afterPath="$PROJECT_DIR$/templates/base.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/blog.html" beforeDir="false" afterPath="$PROJECT_DIR$/templates/blog.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/calculators/finanza_interesse-composto.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/calculators/finanza_ral.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/calculators/finanza_stipendio-netto.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/calculators/finanza_tfr.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/calculators/fitness-salute_bmi.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/calculators/fitness-salute_ovulazione.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/calculators/matematica_percentuale.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/calculators/matematica_scientifico.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/chi-siamo.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/contact.html" beforeDir="false" afterPath="$PROJECT_DIR$/templates/contact.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/datenschutz.html" beforeDir="false" afterPath="$PROJECT_DIR$/templates/datenschutz.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanza/imu.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanza/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanza/iva.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanza/pensione.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanza/stipendio-netto.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanza/tasse-partita-iva.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanzen/brutto-netto.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanzen/gewerbesteuer.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanzen/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanzen/kredit.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanzen/mwst.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanzen/rente.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanzen/steuer.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/finanzen/zinseszins.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/fitness-salute/ciclo.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/fitness-salute/fabbisogno-calorico.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/fitness-salute/gravidanza.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/fitness-salute/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/fitness-salute/metabolismo.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/fitness-salute/ovulazione.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/gesundheit-fitness/alkohol.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/gesundheit-fitness/bmi.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/gesundheit-fitness/eisprung.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/gesundheit-fitness/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/gesundheit-fitness/kalorien.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/gesundheit-fitness/kalorienbedarf.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/gesundheit-fitness/schwangerschaft.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/gesundheit-fitness/ssw.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/impressum.html" beforeDir="false" afterPath="$PROJECT_DIR$/templates/impressum.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/templates/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/kontakt.html" beforeDir="false" afterPath="$PROJECT_DIR$/templates/kontakt.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/matematica/frazioni.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/matematica/grafica.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/matematica/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/matematica/proporzioni.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/matematica/sconto.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/mathematik/dreisatz.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/mathematik/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/mathematik/nullstellen.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/mathematik/pq-formel.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/mathematik/prozent.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/mathematik/pythagoras.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/mathematik/stammfunktion.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/mathematik/wissenschaftlich.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/nutzungsbedingungen.html" beforeDir="false" afterPath="$PROJECT_DIR$/templates/nutzungsbedingungen.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/privacy.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/terms.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/ueber-uns.html" beforeDir="false" afterPath="$PROJECT_DIR$/templates/ueber-uns.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/weitere/arbeitszeit.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/weitere/datum.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/weitere/iban.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/weitere/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/weitere/kfz-steuer.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/weitere/paypal.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/weitere/spritverbrauch.html" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectId" id="2scX3AthewKIKlCdwzmMNNpESff" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="settings.editor.selected.configurable" value="com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable" />
  </component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\code8\static" />
    </key>
  </component>
  <component name="RunManager" selected="Python.code8">
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <module name="code8" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="code8" type="PythonConfigurationType" factoryName="Python">
      <module name="code8" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/app.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="Flask (app.py)" type="Python.FlaskServer" temporary="true" nameIsGenerated="true">
      <module name="code8" />
      <option name="target" value="$PROJECT_DIR$/app.py" />
      <option name="targetType" value="PATH" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Flask server.Flask (app.py)" />
        <item itemvalue="Python.code8" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b177c77c-df1e-4fe4-9653-dfec08d45ab2" name="Default Changelist" comment="" />
      <created>1738755636221</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1738755636221</updated>
      <workItem from="1738755637781" duration="2917000" />
      <workItem from="1738845011250" duration="81000" />
      <workItem from="1738845095221" duration="1372000" />
      <workItem from="1738924637044" duration="601000" />
      <workItem from="1739068402679" duration="2684000" />
      <workItem from="1739107189773" duration="1303000" />
      <workItem from="1739117558621" duration="102000" />
      <workItem from="1739276417797" duration="1209000" />
      <workItem from="1739360776579" duration="606000" />
      <workItem from="1739452075007" duration="2734000" />
      <workItem from="1739717896418" duration="14000" />
      <workItem from="1739800052586" duration="2862000" />
      <workItem from="1740062166498" duration="1601000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
    <option name="oldMeFiltersMigrated" value="true" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/code8$Flask__app_py_.coverage" NAME="Flask (app.py) Coverage Results" MODIFIED="1738755718431" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="" />
  </component>
</project>