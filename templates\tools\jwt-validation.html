{% extends "base.html" %}

{% block title %}JWT Validation Online | Free JWT Token Validator Tool{% endblock %}

{% block description %}Validate JWT tokens instantly with our free online JWT validator. Verify signatures, check expiration, and ensure token integrity. No installation required.{% endblock %}

{% block meta_keywords %}jwt validation, jwt validation online, JWT token validator, jwt signature verification, validate jwt{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
<link href="{{ url_for('static', filename='css/jwt-validation.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item">Tools</li>
        <li class="breadcrumb-item active" aria-current="page">JWT Validation</li>
    </ol>
</nav>

<!-- Hero Section -->
<section class="hero-section text-center py-5">
    <div class="container">
        <h1 class="hero-title mb-3"><span class="text-primary-light">JWT</span> <span class="text-highlight">Validation</span> <span class="text-primary-light">Online:</span> Verify Token Integrity</h1>
        <p class="hero-subtitle mb-4">Free, secure, and instant JWT token validation right in your browser</p>
    </div>
</section>

<!-- Main Validation Tool Section -->
<section id="validation-tool" class="py-4">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="validator-card">
                    <h2 class="validator-title">JWT Token Validation Tool</h2>
                    
                    <div class="form-group mb-4">
                        <label for="jwt-token-input" class="form-label">Paste JWT Token</label>
                        <textarea id="jwt-token-input" class="form-control" placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c" rows="3"></textarea>
                        <div class="token-info text-muted mt-1 small">
                            <i class="fas fa-info-circle"></i> Enter your JWT token above to validate it instantly
                        </div>
                    </div>
                    
                    <div class="form-group mb-4">
                        <label for="jwt-secret-input" class="form-label">Secret Key or Public Key (Optional)</label>
                        <textarea id="jwt-secret-input" class="form-control" placeholder="Enter your secret key for HS256/HS384/HS512 or public key for RS256/ES256" rows="3"></textarea>
                        <div class="secret-info text-muted mt-1 small">
                            <i class="fas fa-info-circle"></i> Required for full signature verification. Leave empty for structure validation only.
                        </div>
                    </div>
                    
                    <div class="form-group mb-4">
                        <label class="form-label d-block">Validation Options</label>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="check-exp" checked>
                            <label class="form-check-label" for="check-exp">Validate Expiration</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="check-nbf" checked>
                            <label class="form-check-label" for="check-nbf">Validate Not Before</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="check-signature" checked>
                            <label class="form-check-label" for="check-signature">Validate Signature</label>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div class="keyboard-shortcuts small text-muted">
                            <span data-tippy-content="Press Ctrl+Enter to validate"><i class="fas fa-keyboard me-1"></i> Shortcuts: Ctrl+Enter to validate</span>
                        </div>
                        <div class="action-buttons">
                            <button id="validate-button" class="btn btn-primary">
                                <i class="fas fa-check-circle me-1"></i> Validate Token
                            </button>
                            <button id="clear-button" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-times me-1"></i> Clear
                            </button>
                        </div>
                    </div>
                    
                    <!-- Validation Results -->
                    <div id="validation-results" class="validation-results mt-4" style="display: none;">
                        <h3 class="mb-4">Validation Results</h3>
                        
                        <!-- Summary Card -->
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">Summary</h4>
                                <div id="validation-status" class="badge bg-secondary">Pending</div>
                            </div>
                            <div class="card-body">
                                <div class="validation-summary">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <div class="check-item d-flex align-items-center">
                                                <div id="structure-check" class="check-icon me-2">
                                                    <i class="fas fa-circle-notch fa-spin"></i>
                                                </div>
                                                <div class="check-details">
                                                    <h5 class="mb-0">Token Structure</h5>
                                                    <p class="text-muted mb-0 small">Checking valid JWT format</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="check-item d-flex align-items-center">
                                                <div id="signature-check" class="check-icon me-2">
                                                    <i class="fas fa-circle-notch fa-spin"></i>
                                                </div>
                                                <div class="check-details">
                                                    <h5 class="mb-0">Signature</h5>
                                                    <p class="text-muted mb-0 small">Checking signature validity</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="check-item d-flex align-items-center">
                                                <div id="expiry-check" class="check-icon me-2">
                                                    <i class="fas fa-circle-notch fa-spin"></i>
                                                </div>
                                                <div class="check-details">
                                                    <h5 class="mb-0">Expiration (exp)</h5>
                                                    <p class="text-muted mb-0 small">Checking if token is expired</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="check-item d-flex align-items-center">
                                                <div id="nbf-check" class="check-icon me-2">
                                                    <i class="fas fa-circle-notch fa-spin"></i>
                                                </div>
                                                <div class="check-details">
                                                    <h5 class="mb-0">Not Before (nbf)</h5>
                                                    <p class="text-muted mb-0 small">Checking if token is active</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Token Details -->
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h4 class="mb-0">Token Claims</h4>
                                        <button class="btn btn-sm btn-outline-primary copy-btn" data-target="payload-json">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-claims">
                                                <thead>
                                                    <tr>
                                                        <th>Claim</th>
                                                        <th>Value</th>
                                                        <th>Description</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="claims-table-body">
                                                    <!-- Claims will be populated here -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <div class="card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h4 class="mb-0">Token Timeline</h4>
                                    </div>
                                    <div class="card-body">
                                        <div id="timeline-container" class="timeline-container">
                                            <div class="timeline-line"></div>
                                            <div id="timeline-issued" class="timeline-point">
                                                <div class="timeline-marker"></div>
                                                <div class="timeline-label">
                                                    <h5>Issued</h5>
                                                    <p id="issued-date">-</p>
                                                </div>
                                            </div>
                                            <div id="timeline-nbf" class="timeline-point">
                                                <div class="timeline-marker"></div>
                                                <div class="timeline-label">
                                                    <h5>Active From</h5>
                                                    <p id="nbf-date">-</p>
                                                </div>
                                            </div>
                                            <div id="timeline-now" class="timeline-point current">
                                                <div class="timeline-marker"></div>
                                                <div class="timeline-label">
                                                    <h5>Current Time</h5>
                                                    <p id="current-date">-</p>
                                                </div>
                                            </div>
                                            <div id="timeline-exp" class="timeline-point">
                                                <div class="timeline-marker"></div>
                                                <div class="timeline-label">
                                                    <h5>Expires</h5>
                                                    <p id="exp-date">-</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Raw JWT Section -->
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">JWT Structure</h4>
                            </div>
                            <div class="card-body">
                                <div class="jwt-structure-visual">
                                    <div class="jwt-part header">
                                        <div class="jwt-part-label">HEADER</div>
                                        <div id="jwt-header-content" class="jwt-part-content"></div>
                                    </div>
                                    <div class="jwt-dot">.</div>
                                    <div class="jwt-part payload">
                                        <div class="jwt-part-label">PAYLOAD</div>
                                        <div id="jwt-payload-content" class="jwt-part-content"></div>
                                    </div>
                                    <div class="jwt-dot">.</div>
                                    <div class="jwt-part signature">
                                        <div class="jwt-part-label">SIGNATURE</div>
                                        <div id="jwt-signature-status" class="jwt-part-content"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Content Sections for SEO - To be filled -->
<section class="content-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <h2>What is JWT Validation?</h2>
                <p>JWT validation is the process of verifying that a JSON Web Token (JWT) is authentic, has not been tampered with, and has not expired. This crucial security step ensures that the token presented by a client is legitimate and can be trusted.</p>
                
                <h3 class="mt-4">Key Components of JWT Validation</h3>
                <div class="list-group mb-4">
                    <div class="list-group-item">
                        <h4><i class="fas fa-check-circle text-success me-2"></i>Structure Validation</h4>
                        <p class="mb-0">Ensures the JWT has the correct format with three parts: header, payload, and signature.</p>
                    </div>
                    <div class="list-group-item">
                        <h4><i class="fas fa-fingerprint text-success me-2"></i>Signature Verification</h4>
                        <p class="mb-0">Confirms the token hasn't been modified by validating the signature against the header and payload using the secret key.</p>
                    </div>
                    <div class="list-group-item">
                        <h4><i class="fas fa-clock text-success me-2"></i>Expiration Checking</h4>
                        <p class="mb-0">Verifies the token hasn't expired by checking the 'exp' claim against the current time.</p>
                    </div>
                    <div class="list-group-item">
                        <h4><i class="fas fa-calendar-check text-success me-2"></i>Not Before Validation</h4>
                        <p class="mb-0">Ensures the token is not used before its valid time by checking the 'nbf' claim.</p>
                    </div>
                </div>
                
                <h3>Why Validate JWT Tokens?</h3>
                <p>JWT validation is essential for maintaining security in your applications. Without proper validation, attackers could:</p>
                <ul class="feature-list mb-4">
                    <li>Forge authentication tokens</li>
                    <li>Modify token payloads to gain unauthorized access</li>
                    <li>Use expired tokens to maintain access</li>
                    <li>Exploit tokens that weren't meant to be active yet</li>
                </ul>
                
                <h3>Common JWT Validation Issues</h3>
                <p>Be aware of these common pitfalls when implementing JWT validation:</p>
                <div class="alert alert-warning">
                    <h4><i class="fas fa-exclamation-triangle me-2"></i>Missing Signature Verification</h4>
                    <p class="mb-0">Some implementations only decode the JWT without verifying the signature, which leaves applications vulnerable to token tampering.</p>
                </div>
                
                <div class="alert alert-warning">
                    <h4><i class="fas fa-exclamation-triangle me-2"></i>Ignoring Expiration Claims</h4>
                    <p class="mb-0">Failing to check the 'exp' claim allows expired tokens to be used indefinitely.</p>
                </div>
                
                <div class="alert alert-warning">
                    <h4><i class="fas fa-exclamation-triangle me-2"></i>Weak Signature Algorithms</h4>
                    <p class="mb-0">Using weak algorithms like 'none' or insecure keys makes tokens susceptible to attacks.</p>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card code-example-card mb-4">
                    <div class="card-header">
                        <h3 class="mb-0">JWT Validation in Python</h3>
                    </div>
                    <div class="card-body">
                        <pre class="language-python"><code>import jwt
from datetime import datetime

def validate_jwt(token, secret_key):
    try:
        # Decode and verify token
        payload = jwt.decode(
            token,
            secret_key,
            algorithms=['HS256'],
            options={
                'verify_signature': True,
                'verify_exp': True,
                'verify_nbf': True
            }
        )
        return True, payload
    except jwt.ExpiredSignatureError:
        return False, "Token expired"
    except jwt.InvalidTokenError:
        return False, "Invalid token"
</code></pre>
                    </div>
                </div>
                
                <div class="card code-example-card mb-4">
                    <div class="card-header">
                        <h3 class="mb-0">JWT Validation in JavaScript</h3>
                    </div>
                    <div class="card-body">
                        <pre class="language-javascript"><code>const jwt = require('jsonwebtoken');

function validateJwt(token, secretKey) {
  try {
    // Verify token
    const decoded = jwt.verify(token, secretKey, {
      algorithms: ['HS256'],
      complete: true
    });
    return { valid: true, payload: decoded.payload };
  } catch (error) {
    return {
      valid: false,
      error: error.message
    };
  }
}</code></pre>
                    </div>
                </div>
                
                <div class="card code-example-card">
                    <div class="card-header">
                        <h3 class="mb-0">Spring Boot JWT Validation</h3>
                    </div>
                    <div class="card-body">
                        <pre class="language-java"><code>public Claims validateToken(String token) {
    try {
        return Jwts.parser()
            .setSigningKey(secretKey)
            .parseClaimsJws(token)
            .getBody();
    } catch (ExpiredJwtException e) {
        throw new TokenExpiredException();
    } catch (JwtException e) {
        throw new InvalidTokenException();
    }
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- More Resources Section -->
<section class="resources-section py-5">
    <div class="container">
        <h2 class="text-center mb-5">JWT Validation Resources</h2>
        
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="resource-card">
                    <div class="resource-icon text-center py-3">
                        <i class="fas fa-book fa-3x text-primary"></i>
                    </div>
                    <div class="resource-content">
                        <h3>JWT Validation Documentation</h3>
                        <p>Comprehensive guides and best practices for JWT validation across different platforms and languages.</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="resource-card">
                    <div class="resource-icon text-center py-3">
                        <i class="fas fa-code fa-3x text-primary"></i>
                    </div>
                    <div class="resource-content">
                        <h3>GitHub Code Examples</h3>
                        <p>Browse open-source JWT validation implementations with examples for JavaScript, Python, Java, and more.</p>
                        <a href="https://github.com/topics/jwt-validation" class="btn btn-outline-primary mt-2" target="_blank">View on GitHub</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="resource-card">
                    <div class="resource-icon text-center py-3">
                        <i class="fas fa-tools fa-3x text-primary"></i>
                    </div>
                    <div class="resource-content">
                        <h3>JWT Validation Tools</h3>
                        <p>Discover popular libraries and frameworks that simplify JWT validation in your applications.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/prismjs/1.24.1/prism.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prismjs/1.24.1/components/prism-python.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prismjs/1.24.1/components/prism-javascript.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prismjs/1.24.1/components/prism-java.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/tippy.js@6/dist/tippy-bundle.umd.min.js"></script>
<script src="{{ url_for('static', filename='js/jwt-validation.js') }}"></script>
{% endblock %} 