/* Blog Page Styles */
.blog-post {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem 0;
}

.blog-post-header {
    margin-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1.5rem;
}

.blog-post-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #212529;
    font-weight: 700;
}

.blog-post-meta {
    color: #6c757d;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.blog-post-meta span {
    margin-right: 1.5rem;
}

.blog-post-intro {
    margin-bottom: 2rem;
    font-size: 1.1rem;
    color: #495057;
}

.blog-post-content h2 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-size: 1.8rem;
    color: #212529;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.blog-post-content h3 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-size: 1.4rem;
    color: #343a40;
}

.blog-post-content p {
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.blog-post-content ul, 
.blog-post-content ol {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
}

.blog-post-content li {
    margin-bottom: 0.5rem;
    line-height: 1.7;
}

.code-block {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.code-block pre {
    margin-bottom: 0;
    padding: 1.5rem;
    overflow-x: auto;
}

.code-block code {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.9rem;
    color: #212529;
    line-height: 1.5;
}

.table-of-contents {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
}

.table-of-contents ul {
    margin-bottom: 0;
}

.table-of-contents a {
    color: #0d6efd;
    text-decoration: none;
}

.table-of-contents a:hover {
    text-decoration: underline;
}

.checklist .form-check {
    padding-left: 2rem;
    margin-bottom: 0.75rem;
}

.diagram {
    text-align: center;
    margin: 2rem 0;
}

.conclusion {
    margin-top: 3rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border-left: 4px solid #0d6efd;
}

.related-posts {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #e9ecef;
}

.related-posts h3 {
    margin-bottom: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .blog-post-title {
        font-size: 2rem;
    }
    
    .blog-post-meta span {
        display: block;
        margin-bottom: 0.5rem;
    }
    
    .blog-post-content h2 {
        font-size: 1.5rem;
    }
    
    .blog-post-content h3 {
        font-size: 1.3rem;
    }
}

/* Breadcrumb styling */
.breadcrumb {
    padding: 0.75rem 0;
    margin-bottom: 2rem;
    background-color: transparent;
    border-bottom: 1px solid #e9ecef;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
}

.breadcrumb a {
    color: #0d6efd;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #6c757d;
} 