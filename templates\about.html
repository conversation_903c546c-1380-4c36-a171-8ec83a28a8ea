{% extends "base.html" %}

{% block title %}About Us | JWT Decode Online{% endblock %}

{% block description %}Learn more about JWT Decode Online. We provide free tools for JWT token decoding, validation, and generation to help developers implement secure authentication solutions.{% endblock %}

{% block meta_keywords %}jwt decode online, about us, jwt tools, jwt token decoder, jwt security{% endblock %}

{% block content %}
<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item active" aria-current="page">About Us</li>
    </ol>
</nav>

<!-- About Hero Section -->
<section class="about-hero py-5 mb-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto text-center">
                <h1 class="display-4 mb-4">About JWT Decode Online</h1>
                <p class="lead mb-3">We create user-friendly and effective tools for JSON Web Token (JWT) operations.</p>
                <p>Our mission is to make JWT authentication understanding and implementation accessible to all developers.</p>
            </div>
        </div>
    </div>
</section>

<!-- Our Story Section -->
<section class="our-story py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h2 class="section-title mb-3 text-center">Our Story</h2>
                <p class="mb-3">We began working on jwtdecode.online in 2023, aiming to create a convenient and accessible tool for JWT operations. Our team consists of experienced developers and security specialists who understand the importance of having reliable tools for learning and implementing JWT authentication in modern applications.</p>
                <p>As authentication continues to evolve, we strive to provide the most up-to-date and secure tools for developers across all platforms.</p>
            </div>
        </div>
    </div>
</section>

<!-- Our Mission Section -->
<section class="our-mission py-4 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h2 class="section-title mb-3 text-center">Our Mission</h2>
                <p class="mb-3">We strive to make JWT authentication simple and accessible for all developers. Our tools help:</p>
                <ul class="list-group mb-3">
                    <li class="list-group-item"><i class="fas fa-check-circle text-primary me-2"></i>Developers understand JWT structure and behavior</li>
                    <li class="list-group-item"><i class="fas fa-check-circle text-primary me-2"></i>Security specialists validate token integrity</li>
                    <li class="list-group-item"><i class="fas fa-check-circle text-primary me-2"></i>Teams implement secure authentication flows</li>
                    <li class="list-group-item"><i class="fas fa-check-circle text-primary me-2"></i>Anyone working with JWTs to troubleshoot issues</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section py-4">
    <div class="container">
        <h2 class="section-title text-center mb-4">Why Choose Our Tools</h2>
        <div class="row">
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-lock fa-2x text-primary"></i>
                        </div>
                        <h3 class="card-title h5">Privacy Focused</h3>
                        <p class="card-text">All JWT operations happen in your browser - your tokens never leave your device</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-code fa-2x text-primary"></i>
                        </div>
                        <h3 class="card-title h5">Cross-Platform Support</h3>
                        <p class="card-text">Resources for JWT implementation across multiple languages and frameworks</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-tools fa-2x text-primary"></i>
                        </div>
                        <h3 class="card-title h5">Comprehensive Tools</h3>
                        <p class="card-text">Decode, validate, and generate JWTs with our suite of developer-friendly tools</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="contact-section py-4 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-3">Contact Us</h2>
                <p class="mb-3">Have questions or suggestions? We're always here to help!</p>
                <div class="card py-3 px-4 mb-3">
                    <div class="card-body">
                        <p class="mb-2">
                            <i class="fas fa-envelope me-2"></i>
                            <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                        </p>
                        <p class="mb-0">
                            <i class="fas fa-globe me-2"></i>
                            <a href="https://jwtdecode.online" class="text-decoration-none">jwtdecode.online</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
.about-hero {
    background: linear-gradient(135deg, var(--bs-primary) 0%, #2c4380 100%);
    color: white;
    padding: 40px 0;
    border-radius: 0 0 15px 15px;
}

.section-title {
    color: var(--bs-primary);
    position: relative;
    padding-bottom: 12px;
    margin-bottom: 20px;
}

.section-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background-color: var(--bs-primary);
}

.card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
</style>
{% endblock %} 