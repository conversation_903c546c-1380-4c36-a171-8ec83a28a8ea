{% extends "base.html" %}

{% block title %}Command Line JWT Tools for Developers | JWT Decode Online{% endblock %}

{% block description %}Boost your productivity with these powerful command-line tools for JWT management. Learn essential CLI tools for decoding, verifying, and managing JWTs.{% endblock %}

{% block meta_keywords %}jwt command line, jwt cli, jwt tools, command line jwt, decode jwt terminal, jwt linux, jwt bash{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/blog.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/blog">Blog</a></li>
        <li class="breadcrumb-item active" aria-current="page">Command Line JWT Tools for Developers</li>
    </ol>
</nav>

<article class="blog-post">
    <header class="blog-post-header">
        <h1 class="blog-post-title">Command Line JWT Tools for Developers</h1>
        <div class="blog-post-meta">
            <span class="post-date"><i class="far fa-calendar-alt me-1"></i> Published: March 5, 2025</span>
            <span class="post-author"><i class="far fa-user me-1"></i> By: JWT Decode Team</span>
            <span class="post-category"><i class="far fa-folder me-1"></i> Category: Developer Tools</span>
        </div>
    </header>

    <div class="blog-post-content">
        <div class="blog-post-intro">
            <p class="lead">JSON Web Tokens (JWTs) have become the standard for modern API authentication, but working with them can be challenging. Command-line tools offer developers a quick and efficient way to decode, verify, and manage JWTs right from the terminal. This guide explores the best CLI tools that will boost your productivity when working with JWTs.</p>
        </div>

        <div class="table-of-contents card my-4">
            <div class="card-header">
                <h2 class="mb-0 h5">Table of Contents</h2>
            </div>
            <div class="card-body">
                <ul>
                    <li><a href="#why-cli-tools">Why Use Command Line Tools for JWT?</a></li>
                    <li><a href="#essential-tools">Essential JWT CLI Tools</a></li>
                    <li><a href="#jwt-cli">JWT-CLI: The Swiss Army Knife</a></li>
                    <li><a href="#jq-base64">JQ + Base64: Native Decoding</a></li>
                    <li><a href="#jose-tools">JOSE Tools for Advanced Cryptography</a></li>
                    <li><a href="#openssl">OpenSSL for JWT Verification</a></li>
                    <li><a href="#custom-scripts">Creating Custom JWT Scripts</a></li>
                    <li><a href="#best-practices">Best Practices and Workflow Tips</a></li>
                </ul>
            </div>
        </div>

        <section id="why-cli-tools">
            <h2>Why Use Command Line Tools for JWT?</h2>
            <p>While web-based tools like JWT Decode Online offer convenience, command-line tools provide distinct advantages for developers:</p>
            
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h3 class="h5 card-title">Speed & Efficiency</h3>
                            <p class="card-text">Quickly decode or verify tokens without switching context from your terminal.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h3 class="h5 card-title">Automation</h3>
                            <p class="card-text">Easily incorporate JWT operations into scripts and CI/CD pipelines.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h3 class="h5 card-title">Security</h3>
                            <p class="card-text">Keep sensitive tokens local to your machine, reducing exposure risk.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h3 class="h5 card-title">Integration</h3>
                            <p class="card-text">Easily pipe JWT operations with other command-line tools for powerful workflows.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="essential-tools">
            <h2>Essential JWT CLI Tools</h2>
            <p>Let's explore the most useful command-line tools for working with JWTs:</p>
            
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th>Tool</th>
                            <th>Language</th>
                            <th>Key Features</th>
                            <th>Best For</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>jwt-cli</strong></td>
                            <td>Rust/Node.js</td>
                            <td>Decoding, encoding, verification</td>
                            <td>General-purpose JWT operations</td>
                        </tr>
                        <tr>
                            <td><strong>jq + base64</strong></td>
                            <td>Shell</td>
                            <td>Lightweight decoding, JSON processing</td>
                            <td>Quick inspections without installation</td>
                        </tr>
                        <tr>
                            <td><strong>jose</strong></td>
                            <td>Node.js</td>
                            <td>Advanced cryptographic operations</td>
                            <td>Complex JWT/JWS/JWE operations</td>
                        </tr>
                        <tr>
                            <td><strong>pyjwt</strong></td>
                            <td>Python</td>
                            <td>Python-based JWT handling</td>
                            <td>Python developers, scripting</td>
                        </tr>
                        <tr>
                            <td><strong>OpenSSL</strong></td>
                            <td>C</td>
                            <td>Key generation, manual verification</td>
                            <td>Cryptographic operations</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <section id="jwt-cli">
            <h2>JWT-CLI: The Swiss Army Knife</h2>
            <p><a href="https://github.com/mike-engel/jwt-cli" target="_blank">JWT-CLI</a> is arguably the most user-friendly dedicated JWT tool for the command line. It's fast, feature-rich, and available for most platforms.</p>
            
            <h3>Installation</h3>
            <div class="code-block">
                <pre><code># Using npm (Node.js version)
npm install -g jwt-cli

# Using Homebrew (macOS)
brew install mike-engel/jwt-cli/jwt-cli

# Using Cargo (Rust version)
cargo install jwt-cli</code></pre>
            </div>
            
            <h3>Basic Usage</h3>
            <div class="code-block">
                <pre><code># Decode a JWT
jwt decode eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c

# Encode a new JWT
jwt encode --secret your-secret-key --payload '{"sub": "1234567890", "name": "John Doe", "iat": 1516239022}'

# Verify a JWT with a secret
jwt verify --secret your-secret-key eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c</code></pre>
            </div>
            
            <h3>Advanced Features</h3>
            <p>JWT-CLI offers several advanced features, including:</p>
            <ul>
                <li>Support for different algorithms (HS256, RS256, etc.)</li>
                <li>RSA and ECDSA key pair support</li>
                <li>Pretty-printing of JWT claims</li>
                <li>Colorized output</li>
                <li>Detailed expiration information</li>
            </ul>
            
            <div class="code-block">
                <pre><code># Using RS256 with a private key
jwt encode --alg RS256 --secret ./private-key.pem --payload '{"sub": "1234567890"}'

# Verifying with a public key
jwt verify --alg RS256 --secret ./public-key.pem [TOKEN]</code></pre>
            </div>
        </section>

        <section id="jq-base64">
            <h2>JQ + Base64: Native Decoding</h2>
            <p>For those who prefer lightweight solutions using standard Unix tools, JWTs can be decoded with a combination of <code>base64</code> and <code>jq</code>:</p>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Note:</strong> This approach doesn't verify the JWT signature but is excellent for quick inspections.
            </div>
            
            <h3>Basic Decoding</h3>
            <div class="code-block">
                <pre><code># Create a shell function for JWT decoding
function jwt-decode() {
    jq -R 'split(".") | .[0],.[1] | @base64d | fromjson' <<< "$1"
}

# Usage
jwt-decode eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c</code></pre>
            </div>
            
            <p>For a one-liner that doesn't require a function definition:</p>
            
            <div class="code-block">
                <pre><code># Decode JWT header
echo -n "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9" | base64 -d | jq

# Decode JWT payload (may need to add padding)
echo -n "eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ" | base64 -d | jq</code></pre>
            </div>
            
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Important:</strong> JWT base64url encoding might require handling of padding and replacing characters before decoding. The function above handles this automatically.
            </div>
            
            <h3>Advanced JQ Processing</h3>
            <p>JQ's power comes from its ability to process and transform JSON. Here's how to extract specific claims:</p>
            
            <div class="code-block">
                <pre><code># Extract only the 'sub' claim from payload
function jwt-extract-sub() {
    jq -R 'split(".") | .[1] | @base64d | fromjson | .sub' <<< "$1"
}

# Check if a token is expired
function jwt-is-expired() {
    local exp=$(jq -R 'split(".") | .[1] | @base64d | fromjson | .exp' <<< "$1")
    local now=$(date +%s)
    
    if [[ $exp -lt $now ]]; then
        echo "Token expired at $(date -d @$exp)"
        return 0
    else
        echo "Token valid until $(date -d @$exp)"
        return 1
    fi
}</code></pre>
            </div>
        </section>

        <section id="jose-tools">
            <h2>JOSE Tools for Advanced Cryptography</h2>
            <p>The <a href="https://github.com/panva/jose" target="_blank">JOSE</a> (JavaScript Object Signing and Encryption) toolkit provides comprehensive support for advanced JWT operations:</p>
            
            <h3>Installation</h3>
            <div class="code-block">
                <pre><code># Install Node.js JOSE CLI
npm install -g jose-cli</code></pre>
            </div>
            
            <h3>Key Features</h3>
            <ul>
                <li>Complete support for JWS (JSON Web Signature)</li>
                <li>Support for JWE (JSON Web Encryption)</li>
                <li>Key generation and management</li>
                <li>JWKS (JSON Web Key Set) handling</li>
                <li>Support for all standard algorithms</li>
            </ul>
            
            <div class="code-block">
                <pre><code># Generate a JWT with jose
jose sign --alg HS256 --secret your-secret-key '{"sub": "user123", "role": "admin"}'

# Verify a JWT
jose verify --alg HS256 --secret your-secret-key "your.jwt.token"

# Generate an RSA key pair
jose generate-key-pair --alg RS256 --use sig --output-public public.jwk --output-private private.jwk

# Create a JWT using RSA
jose sign --alg RS256 --key private.jwk '{"sub": "user123"}'</code></pre>
            </div>
            
            <h3>Encrypted JWTs</h3>
            <p>One of JOSE's standout features is its support for JWE (encrypted tokens):</p>
            
            <div class="code-block">
                <pre><code># Encrypt a JWT payload
jose encrypt --alg RSA-OAEP-256 --enc A256GCM --key public.jwk '{"sensitive": "data"}'

# Decrypt a JWE
jose decrypt --key private.jwk "encrypted.token"</code></pre>
            </div>
        </section>

        <section id="openssl">
            <h2>OpenSSL for JWT Verification</h2>
            <p>OpenSSL is a powerful tool for working with cryptography, including JWT signature verification:</p>
            
            <h3>Manual Verification with OpenSSL</h3>
            <div class="code-block">
                <pre><code># For RS256 tokens - Verify signature manually
# 1. Extract header and payload
HEADER_PAYLOAD=$(echo -n "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIn0" | tr -d '=')

# 2. Extract the signature
SIGNATURE=$(echo -n "c4hiUPoj9qkSMuTIm9NHKeoQHkFsnoGTdNN6DfJI..." | base64 -d | xxd -p -c 256)

# 3. Verify using OpenSSL
echo -n $HEADER_PAYLOAD | openssl dgst -sha256 -verify public.pem -signature <(echo $SIGNATURE | xxd -r -p)</code></pre>
            </div>
            
            <h3>Generate Keys for JWT</h3>
            <div class="code-block">
                <pre><code># Generate a private key
openssl genrsa -out private.pem 2048

# Extract the public key
openssl rsa -in private.pem -pubout -out public.pem</code></pre>
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Tip:</strong> While OpenSSL is powerful, dedicated JWT tools are generally easier to use for standard tasks. OpenSSL is best for key management and custom cryptographic operations.
            </div>
        </section>

        <section id="custom-scripts">
            <h2>Creating Custom JWT Scripts</h2>
            <p>For specialized workflows, creating custom scripts can be highly effective. Here's a simple but powerful Bash script that combines several tools:</p>
            
            <div class="code-block">
                <pre><code>#!/bin/bash
# jwt-tools.sh - Custom JWT toolkit

function jwt-help {
    echo "JWT Command Line Tools"
    echo "---------------------"
    echo "Usage:"
    echo "  jwt-tools decode <token>          - Decode JWT without verification"
    echo "  jwt-tools verify <token> <secret> - Verify JWT signature"
    echo "  jwt-tools check-exp <token>       - Check if JWT is expired"
    echo "  jwt-tools extract <token> <claim> - Extract specific claim"
}

function jwt-decode {
    local token=$1
    local header=$(echo -n ${token%.*.*} | base64 -d 2>/dev/null || echo "Invalid header")
    local payload=$(echo -n ${token#*.} | cut -d. -f1 | base64 -d 2>/dev/null || echo "Invalid payload")
    
    echo "Header:"
    echo $header | jq . || echo $header
    echo -e "\nPayload:"
    echo $payload | jq . || echo $payload
}

function jwt-verify {
    # Simplified verification - use a proper library for production
    local token=$1
    local secret=$2
    
    # Implementation depends on algorithm
    # This is a simplified example
    echo "Verification would happen here"
}

function jwt-check-exp {
    local token=$1
    local payload=$(echo -n ${token#*.} | cut -d. -f1 | base64 -d 2>/dev/null)
    
    # Extract exp claim
    local exp=$(echo $payload | jq -r '.exp // empty')
    
    if [[ -z "$exp" ]]; then
        echo "No expiration claim found"
        return
    fi
    
    local now=$(date +%s)
    
    if [[ $exp -lt $now ]]; then
        echo "TOKEN EXPIRED at $(date -d @$exp)"
        return 1
    else
        local diff=$((exp - now))
        echo "Token valid for $diff more seconds (expires $(date -d @$exp))"
        return 0
    fi
}

function jwt-extract {
    local token=$1
    local claim=$2
    
    local payload=$(echo -n ${token#*.} | cut -d. -f1 | base64 -d 2>/dev/null)
    echo $payload | jq -r ".$claim // \"Claim not found\""
}

# Main command router
case "$1" in
    decode)
        jwt-decode "$2"
        ;;
    verify)
        jwt-verify "$2" "$3"
        ;;
    check-exp)
        jwt-check-exp "$2"
        ;;
    extract)
        jwt-extract "$2" "$3"
        ;;
    *)
        jwt-help
        ;;
esac</code></pre>
            </div>
            
            <h3>Using Python for Custom Scripts</h3>
            <p>For more complex processing, Python with the PyJWT library offers a good balance of readability and power:</p>
            
            <div class="code-block">
                <pre><code>#!/usr/bin/env python3
# jwt_tool.py

import jwt
import sys
import json
import time
from datetime import datetime

def decode_token(token, verify=False, secret=None):
    try:
        if verify and secret:
            decoded = jwt.decode(token, secret, algorithms=["HS256"])
            print("✓ Signature verified")
        else:
            decoded = jwt.decode(token, options={"verify_signature": False})
            print("⚠ Token decoded without signature verification")
            
        header = jwt.get_unverified_header(token)
        
        print("\nHEADER:")
        print(json.dumps(header, indent=2))
        
        print("\nPAYLOAD:")
        print(json.dumps(decoded, indent=2))
        
        # Check expiration
        if 'exp' in decoded:
            exp_time = datetime.fromtimestamp(decoded['exp'])
            now = datetime.now()
            if exp_time > now:
                print(f"\nToken expires at: {exp_time} (valid for {(exp_time - now).total_seconds()} seconds)")
            else:
                print(f"\n⚠ Token EXPIRED at: {exp_time}")
    
    except jwt.ExpiredSignatureError:
        print("❌ Token has expired")
    except jwt.InvalidTokenError as e:
        print(f"❌ Invalid token: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: jwt_tool.py <token> [--verify] [--secret SECRET]")
        sys.exit(1)
        
    token = sys.argv[1]
    verify = "--verify" in sys.argv
    
    secret = None
    if "--secret" in sys.argv:
        secret_index = sys.argv.index("--secret") + 1
        if secret_index < len(sys.argv):
            secret = sys.argv[secret_index]
    
    decode_token(token, verify, secret)</code></pre>
            </div>
            
            <div class="alert alert-primary">
                <i class="fas fa-lightbulb me-2"></i>
                <strong>Pro Tip:</strong> Make your script executable (<code>chmod +x jwt_tool.py</code>) and add it to your PATH for easy access from anywhere in your terminal.
            </div>
        </section>

        <section id="best-practices">
            <h2>Best Practices and Workflow Tips</h2>
            <p>Here are some tips for working efficiently with JWT command-line tools:</p>
            
            <h3>Recommended Workflow</h3>
            <ol>
                <li><strong>Keep tokens in environment variables</strong> for easy access without exposing them in command history</li>
                <li><strong>Create token templates</strong> for testing different scenarios</li>
                <li><strong>Use aliases</strong> for common JWT operations in your shell configuration</li>
                <li><strong>Combine with other tools</strong> like <code>curl</code> for API testing</li>
                <li><strong>Store keys securely</strong> with tools like <code>keyring</code> or <code>pass</code></li>
            </ol>
            
            <h3>Security Considerations</h3>
            <ul>
                <li>Be cautious with command history when working with sensitive tokens</li>
                <li>Use <code>HISTIGNORE</code> in bash to prevent storing certain commands</li>
                <li>Don't hardcode secrets in scripts</li>
                <li>Verify signatures when handling user-supplied tokens</li>
            </ul>
            
            <div class="code-block">
                <pre><code># Add to .bashrc or .zshrc for safer JWT operations
export HISTIGNORE="*jwt*"

# Store JWT in environment variable
export MY_JWT="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIn0.Rq8IxqeIVXgw1beavDuBkopHNUlz1XM2SVi-kOU-uKY"

# Use with curl
curl -H "Authorization: Bearer $MY_JWT" https://api.example.com/protected-resource</code></pre>
            </div>
            
            <h3>Integration with Development Workflow</h3>
            <p>JWT tools can be integrated into your development workflow for more efficient testing:</p>
            
            <div class="code-block">
                <pre><code># Generate a test token for local development
function dev-token() {
    local payload="{\"sub\":\"developer\",\"role\":\"admin\",\"exp\":$(($(date +%s) + 3600))}"
    jwt encode --secret development-secret --payload "$payload"
}

# Test API with generated token
function test-api() {
    local token=$(dev-token)
    curl -H "Authorization: Bearer $token" http://localhost:3000/api/protected
}</code></pre>
            </div>
        </section>

        <div class="conclusion">
            <h2>Conclusion</h2>
            <p>Command-line JWT tools are an invaluable addition to any developer's toolkit. They provide quick, efficient ways to work with JWTs without leaving your terminal, enabling faster debugging and more streamlined workflows.</p>
            
            <p>For most developers, we recommend starting with <strong>jwt-cli</strong> for its balance of features and ease of use. As your needs grow, explore the other tools mentioned in this article to find the perfect fit for your specific requirements.</p>
            
            <p>Remember that while these tools make JWT handling easier, it's still essential to understand the security implications of JWT usage in your applications. Always follow best practices for token management, especially when dealing with authentication and authorization.</p>
        </div>

        <div class="related-posts mt-5">
            <h3>Related Articles</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Understanding JWT in React Applications</h5>
                            <p class="card-text">Learn how to implement JWT authentication in React apps for secure user sessions.</p>
                            <a href="/blog/jwt-in-react-applications" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">JWT Authentication in Flutter Apps</h5>
                            <p class="card-text">Step-by-step guide to implementing JWT authentication in your Flutter applications.</p>
                            <a href="/blog/jwt-authentication-flutter" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">JWT vs Session Tokens: Which to Choose?</h5>
                            <p class="card-text">A detailed comparison of JWT and session-based authentication approaches.</p>
                            <a href="/blog/jwt-vs-session-tokens" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</article>
{% endblock %} 