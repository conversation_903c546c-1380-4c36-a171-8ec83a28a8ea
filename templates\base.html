<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}JWT Decode Online | Free JWT Token Decoder Tool{% endblock %}</title>
    <meta name="description" content="{% block description %}Decode JWT tokens instantly with our free online JWT decoder. Support for JavaScript, Flutter, Java, React, and more. No installation required.{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}jwt decode, jwt decode online, JWT token, jwt decode javascript, jwt decode flutter{% endblock %}">
    <meta name="author" content="jwtdecode.online">
    <meta name="robots" content="index, follow">
    
    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/main.css') }}" rel="stylesheet">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-key me-2"></i>
                <span>JWT Decode Online</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="toolsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Tools
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="toolsDropdown">
                            <li><a class="dropdown-item" href="/">JWT Decode Online</a></li>
                            <li><a class="dropdown-item" href="/tools/jwt-validation">JWT Validation</a></li>
                            <li><a class="dropdown-item" href="/tools/jwt-generator">JWT Generator</a></li>
                            <li><a class="dropdown-item" href="/tools/jwt-decode-offline">Offline Version</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/how-it-works">How It Works</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/blog">Blog</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>JWT Decode Online</h5>
                    <p class="mb-3">Simple, secure, and instant JWT token decoding</p>
                </div>
                <div class="col-md-6">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="/" class="text-white">Home</a></li>
                        <li><a href="/how-it-works" class="text-white">How It Works</a></li>
                        <li><a href="/tools/jwt-validation" class="text-white">JWT Validation</a></li>
                        <li><a href="/tools/jwt-generator" class="text-white">JWT Generator</a></li>
                        <li><a href="/blog" class="text-white">Blog</a></li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; {{ format_date().split('/')[2] }} JWT Decode Online. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html> 