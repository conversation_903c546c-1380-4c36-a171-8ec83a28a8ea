{% extends "base.html" %}

{% block title %}JWT Authentication in Flutter Apps | JWT Decode Online{% endblock %}

{% block description %}Step-by-step guide to implementing JWT authentication in your Flutter applications. Learn best practices for secure user authentication.{% endblock %}

{% block meta_keywords %}jwt flutter, flutter authentication, flutter jwt token, flutter auth, jwt in flutter apps{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/blog.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item"><a href="/blog">Blog</a></li>
        <li class="breadcrumb-item active" aria-current="page">JWT Authentication in Flutter Apps</li>
    </ol>
</nav>

<article class="blog-post">
    <header class="blog-post-header">
        <h1 class="blog-post-title">JWT Authentication in Flutter Apps</h1>
        <div class="blog-post-meta">
            <span class="post-date"><i class="far fa-calendar-alt me-1"></i> Published: March 10, 2025</span>
            <span class="post-author"><i class="far fa-user me-1"></i> By: JWT Decode Team</span>
            <span class="post-category"><i class="far fa-folder me-1"></i> Category: Mobile Development</span>
        </div>
    </header>

    <div class="blog-post-content">
        <div class="blog-post-intro">
            <p class="lead">Implementing secure authentication is a crucial aspect of any mobile application. JSON Web Tokens (JWT) provide a modern, stateless approach to authentication that works exceptionally well with Flutter applications. This guide will walk you through implementing JWT authentication in Flutter apps from start to finish.</p>
        </div>

        <div class="table-of-contents card my-4">
            <div class="card-header">
                <h2 class="mb-0 h5">Table of Contents</h2>
            </div>
            <div class="card-body">
                <ul>
                    <li><a href="#understanding-jwt">Understanding JWT in Mobile Context</a></li>
                    <li><a href="#flutter-auth-flow">Authentication Flow in Flutter</a></li>
                    <li><a href="#setup-dependencies">Setting Up Dependencies</a></li>
                    <li><a href="#auth-service">Creating the Authentication Service</a></li>
                    <li><a href="#secure-storage">Secure Token Storage</a></li>
                    <li><a href="#login-screen">Building the Login Screen</a></li>
                    <li><a href="#protected-routes">Implementing Protected Routes</a></li>
                    <li><a href="#token-refresh">Token Refresh Mechanism</a></li>
                    <li><a href="#testing">Testing Authentication</a></li>
                </ul>
            </div>
        </div>

        <section id="understanding-jwt">
            <h2>Understanding JWT in Mobile Context</h2>
            <p>JSON Web Tokens (JWT) are particularly well-suited for mobile applications due to their stateless nature and compact size. When using JWT in Flutter applications, you can:</p>
            
            <ul>
                <li>Reduce server load by eliminating session storage</li>
                <li>Support offline authentication validation</li>
                <li>Scale your backend more easily</li>
                <li>Create a consistent auth experience across platforms</li>
            </ul>

            <p>A typical JWT consists of three parts separated by dots: header, payload, and signature.</p>
            
            <div class="code-block">
                <pre><code>// Example JWT structure
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c

// Decoded parts:
// Header: {"alg":"HS256","typ":"JWT"}
// Payload: {"sub":"1234567890","name":"John Doe","iat":1516239022}
// Signature: HMACSHA256(base64UrlEncode(header) + "." + base64UrlEncode(payload), secret)</code></pre>
            </div>
        </section>

        <section id="flutter-auth-flow">
            <h2>Authentication Flow in Flutter</h2>
            <p>The JWT authentication flow in a Flutter application typically works as follows:</p>
            
            <ol>
                <li>User enters credentials on the login screen</li>
                <li>App sends credentials to authentication server</li>
                <li>Server validates credentials and returns JWT tokens (access token and refresh token)</li>
                <li>App securely stores the tokens using Flutter Secure Storage</li>
                <li>For subsequent API requests, the access token is included in the Authorization header</li>
                <li>When the access token expires, the app uses the refresh token to get a new access token</li>
                <li>On logout, tokens are removed from storage</li>
            </ol>

            <div class="diagram my-4">
                <img src="{{ url_for('static', filename='img/flutter-jwt-flow.png') }}" alt="Flutter JWT Authentication Flow" class="img-fluid">
            </div>
        </section>

        <section id="setup-dependencies">
            <h2>Setting Up Dependencies</h2>
            <p>Start by adding the necessary dependencies to your <code>pubspec.yaml</code> file:</p>
            
            <div class="code-block">
                <pre><code>dependencies:
  flutter:
    sdk: flutter
  http: ^0.13.4
  flutter_secure_storage: ^5.0.2
  provider: ^6.0.2
  jwt_decoder: ^2.0.1</code></pre>
            </div>
            
            <p>Each of these packages serves a specific purpose:</p>
            <ul>
                <li><strong>http</strong>: For making API requests to your authentication server</li>
                <li><strong>flutter_secure_storage</strong>: For securely storing JWT tokens</li>
                <li><strong>provider</strong>: For state management of authentication state</li>
                <li><strong>jwt_decoder</strong>: For decoding and validating JWT tokens</li>
            </ul>
            
            <p>After adding these dependencies, run:</p>
            <div class="code-block">
                <pre><code>flutter pub get</code></pre>
            </div>
        </section>

        <section id="auth-service">
            <h2>Creating the Authentication Service</h2>
            <p>Create an authentication service class to handle all authentication-related operations:</p>
            
            <div class="code-block">
                <pre><code>// lib/services/auth_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:jwt_decoder/jwt_decoder.dart';

class AuthService {
  final String baseUrl = 'https://your-api.com/auth';
  final storage = FlutterSecureStorage();

  // Keys for storage
  final String accessTokenKey = 'access_token';
  final String refreshTokenKey = 'refresh_token';

  // Login method
  Future<bool> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/login'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'email': email,
          'password': password,
        }),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        
        // Store tokens
        await storage.write(key: accessTokenKey, value: responseData['access_token']);
        await storage.write(key: refreshTokenKey, value: responseData['refresh_token']);
        
        return true;
      }
      return false;
    } catch (e) {
      print('Login error: $e');
      return false;
    }
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    try {
      final token = await storage.read(key: accessTokenKey);
      
      if (token == null) {
        return false;
      }
      
      // Check if token is expired
      bool isExpired = JwtDecoder.isExpired(token);
      
      if (isExpired) {
        // Try to refresh token
        bool refreshed = await refreshToken();
        return refreshed;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Get the access token
  Future<String?> getAccessToken() async {
    return await storage.read(key: accessTokenKey);
  }

  // Refresh token
  Future<bool> refreshToken() async {
    try {
      final refreshToken = await storage.read(key: refreshTokenKey);
      
      if (refreshToken == null) {
        return false;
      }
      
      final response = await http.post(
        Uri.parse('$baseUrl/refresh'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'refresh_token': refreshToken,
        }),
      );
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        
        // Store new access token
        await storage.write(key: accessTokenKey, value: responseData['access_token']);
        
        // Sometimes the API returns a new refresh token too
        if (responseData.containsKey('refresh_token')) {
          await storage.write(key: refreshTokenKey, value: responseData['refresh_token']);
        }
        
        return true;
      }
      return false;
    } catch (e) {
      print('Token refresh error: $e');
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      // Call logout endpoint if your API has one
      // ...
      
      // Clear stored tokens
      await storage.delete(key: accessTokenKey);
      await storage.delete(key: refreshTokenKey);
    } catch (e) {
      print('Logout error: $e');
    }
  }

  // Get user info from token
  Map<String, dynamic> getUserInfo() {
    try {
      final token = storage.read(key: accessTokenKey);
      if (token != null) {
        return JwtDecoder.decode(token);
      }
      return {};
    } catch (e) {
      return {};
    }
  }
}</code></pre>
            </div>
        </section>

        <section id="secure-storage">
            <h2>Secure Token Storage</h2>
            <p>Flutter Secure Storage provides a secure way to store sensitive information like JWT tokens. It uses:</p>
            
            <ul>
                <li>Keychain on iOS</li>
                <li>EncryptedSharedPreferences on Android</li>
            </ul>
            
            <p>The AuthService class we created above already uses Flutter Secure Storage, but it's important to understand some best practices:</p>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Security Note:</strong> Never store tokens in regular SharedPreferences or local storage, as they are not encrypted and can be accessed by any app with the right permissions.
            </div>
            
            <p>If you need to configure advanced options for secure storage:</p>
            
            <div class="code-block">
                <pre><code>// Advanced configuration example
final storage = FlutterSecureStorage(
  aOptions: AndroidOptions(
    encryptedSharedPreferences: true,
  ),
  iOptions: IOSOptions(
    accessibility: IOSAccessibility.first_unlock,
  ),
);</code></pre>
            </div>
        </section>

        <section id="login-screen">
            <h2>Building the Login Screen</h2>
            <p>Here's an example of a simple login screen that uses the AuthService:</p>
            
            <div class="code-block">
                <pre><code>// lib/screens/login_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String _errorMessage = '';

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    final authService = Provider.of<AuthService>(context, listen: false);
    final success = await authService.login(
      _emailController.text.trim(),
      _passwordController.text,
    );

    setState(() {
      _isLoading = false;
    });

    if (success) {
      // Navigate to home screen on successful login
      Navigator.of(context).pushReplacementNamed('/home');
    } else {
      setState(() {
        _errorMessage = 'Invalid email or password. Please try again.';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Login'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              TextFormField(
                controller: _emailController,
                decoration: InputDecoration(labelText: 'Email'),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(labelText: 'Password'),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your password';
                  }
                  return null;
                },
              ),
              SizedBox(height: 24),
              if (_errorMessage.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: Text(
                    _errorMessage,
                    style: TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                ),
              ElevatedButton(
                onPressed: _isLoading ? null : _submit,
                child: _isLoading
                    ? CircularProgressIndicator(color: Colors.white)
                    : Text('Login'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}</code></pre>
            </div>
        </section>

        <section id="protected-routes">
            <h2>Implementing Protected Routes</h2>
            <p>To protect certain screens or routes in your app, create a wrapper widget that checks for authentication:</p>
            
            <div class="code-block">
                <pre><code>// lib/widgets/auth_wrapper.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';

class AuthWrapper extends StatefulWidget {
  final Widget child;
  final Widget loadingWidget;
  final Widget unauthenticatedWidget;

  AuthWrapper({
    required this.child,
    required this.loadingWidget,
    required this.unauthenticatedWidget,
  });

  @override
  _AuthWrapperState createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  late Future<bool> _authCheckFuture;

  @override
  void initState() {
    super.initState();
    _checkAuth();
  }

  void _checkAuth() {
    final authService = Provider.of<AuthService>(context, listen: false);
    _authCheckFuture = authService.isLoggedIn();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: _authCheckFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return widget.loadingWidget;
        }

        final isAuthenticated = snapshot.data ?? false;
        
        if (isAuthenticated) {
          return widget.child;
        } else {
          return widget.unauthenticatedWidget;
        }
      },
    );
  }
}</code></pre>
            </div>
            
            <p>You can then use this wrapper in your navigation setup:</p>
            
            <div class="code-block">
                <pre><code>// Usage example in main.dart or navigation setup
MaterialApp(
  // ...
  routes: {
    '/home': (context) => AuthWrapper(
      child: HomeScreen(),
      loadingWidget: LoadingScreen(),
      unauthenticatedWidget: LoginScreen(),
    ),
    '/profile': (context) => AuthWrapper(
      child: ProfileScreen(),
      loadingWidget: LoadingScreen(),
      unauthenticatedWidget: LoginScreen(),
    ),
    // Public routes
    '/login': (context) => LoginScreen(),
    '/register': (context) => RegisterScreen(),
  },
  // ...
);</code></pre>
            </div>
        </section>

        <section id="token-refresh">
            <h2>Token Refresh Mechanism</h2>
            <p>To handle token expiration gracefully, implement an HTTP interceptor that automatically refreshes tokens:</p>
            
            <div class="code-block">
                <pre><code>// lib/services/api_client.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'auth_service.dart';

class ApiClient {
  final String baseUrl;
  final AuthService authService;

  ApiClient({required this.baseUrl, required this.authService});

  Future<http.Response> get(String endpoint) async {
    return _sendRequest(() => http.get(
      Uri.parse('$baseUrl/$endpoint'),
      headers: await _getHeaders(),
    ));
  }

  Future<http.Response> post(String endpoint, dynamic data) async {
    return _sendRequest(() => http.post(
      Uri.parse('$baseUrl/$endpoint'),
      headers: await _getHeaders(),
      body: json.encode(data),
    ));
  }

  Future<http.Response> put(String endpoint, dynamic data) async {
    return _sendRequest(() => http.put(
      Uri.parse('$baseUrl/$endpoint'),
      headers: await _getHeaders(),
      body: json.encode(data),
    ));
  }

  Future<http.Response> delete(String endpoint) async {
    return _sendRequest(() => http.delete(
      Uri.parse('$baseUrl/$endpoint'),
      headers: await _getHeaders(),
    ));
  }

  Future<Map<String, String>> _getHeaders() async {
    final token = await authService.getAccessToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  Future<http.Response> _sendRequest(Future<http.Response> Function() request) async {
    try {
      final response = await request();
      
      // If token has expired
      if (response.statusCode == 401) {
        final refreshed = await authService.refreshToken();
        
        if (refreshed) {
          // Retry the request with the new token
          return await request();
        }
      }
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
}</code></pre>
            </div>
        </section>

        <section id="testing">
            <h2>Testing Authentication</h2>
            <p>Testing your authentication flow is crucial. Here's a simple way to write tests for your auth service:</p>
            
            <div class="code-block">
                <pre><code>// test/auth_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:your_app/services/auth_service.dart';

// Create mocks
class MockHttpClient extends Mock implements http.Client {}
class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}

void main() {
  late AuthService authService;
  late MockHttpClient mockHttpClient;
  late MockFlutterSecureStorage mockStorage;

  setUp(() {
    mockHttpClient = MockHttpClient();
    mockStorage = MockFlutterSecureStorage();
    authService = AuthService();
    // Inject mocks
    // ...
  });

  group('AuthService Tests', () {
    test('login should return true on successful login', () async {
      // Test implementation
      // ...
    });

    test('login should return false on failed login', () async {
      // Test implementation
      // ...
    });

    test('isLoggedIn should return true when valid token exists', () async {
      // Test implementation
      // ...
    });

    test('refreshToken should get new token when refresh token is valid', () async {
      // Test implementation
      // ...
    });
  });
}</code></pre>
            </div>
        </section>

        <div class="conclusion">
            <h2>Conclusion</h2>
            <p>Implementing JWT authentication in Flutter applications provides a secure and efficient way to authenticate users. By following the steps outlined in this guide, you can create a robust authentication system that protects your users' data while providing a seamless user experience.</p>
            
            <p>Remember to:</p>
            <ul>
                <li>Always store tokens securely using Flutter Secure Storage</li>
                <li>Implement proper token refresh mechanisms</li>
                <li>Validate tokens on both client and server sides</li>
                <li>Handle authentication errors gracefully</li>
            </ul>
            
            <p>With these foundations in place, you can build secure, production-ready Flutter applications with confidence.</p>
        </div>

        <div class="related-posts mt-5">
            <h3>Related Articles</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Understanding JWT in React Applications</h5>
                            <p class="card-text">Learn how to implement JWT authentication in React apps for secure user sessions.</p>
                            <a href="/blog/jwt-in-react-applications" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">JWT vs Session Tokens: Which to Choose?</h5>
                            <p class="card-text">A detailed comparison of JWT and session-based authentication approaches.</p>
                            <a href="/blog/jwt-vs-session-tokens" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Command Line JWT Tools for Developers</h5>
                            <p class="card-text">Boost your productivity with these powerful command-line tools for JWT management.</p>
                            <a href="/blog/command-line-jwt-tools" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</article>
{% endblock %} 