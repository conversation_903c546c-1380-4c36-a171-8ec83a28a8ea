{% extends "base.html" %}

{% block title %}How JWT Decode Works | Understanding JSON Web Tokens{% endblock %}

{% block description %}Learn how JWT decoding works, understand the structure of JSON Web Tokens, and discover best practices for JWT implementation in your applications.{% endblock %}

{% block meta_keywords %}jwt decode explanation, how jwt works, jwt structure, jwt security, jwt implementation{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="container mt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item active" aria-current="page">How It Works</li>
    </ol>
</nav>

<!-- Main Content -->
<div class="container py-5">
    <article class="how-it-works-content">
        <h1 class="mb-4">Understanding How JWT Decode Works</h1>
        
        <section class="mb-5">
            <h2>What is a JSON Web Token (JWT)?</h2>
            <p>A JSON Web Token (JWT) is an open standard (RFC 7519) that defines a compact and self-contained way for securely transmitting information between parties as a JSON object. JWTs are commonly used for authentication and information exchange in web development.</p>
            
            <div class="jwt-structure-visual my-4">
                <div class="jwt-example p-3 bg-light rounded">
                    <code>eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.<br>eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIn0.<br>SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c</code>
                </div>
            </div>
        </section>

        <section class="mb-5">
            <h2>The Three Parts of a JWT</h2>
            
            <h3 class="mt-4">1. Header</h3>
            <p>The header typically consists of two parts:</p>
            <ul>
                <li>The type of token (JWT)</li>
                <li>The signing algorithm being used (e.g., HMAC SHA256 or RSA)</li>
            </ul>
            <div class="code-example bg-light p-3 rounded mb-3">
                <pre>{
  "alg": "HS256",
  "typ": "JWT"
}</pre>
            </div>

            <h3 class="mt-4">2. Payload</h3>
            <p>The payload contains the claims. Claims are statements about an entity (typically, the user) and additional data. There are three types of claims:</p>
            <ul>
                <li><strong>Registered claims:</strong> Predefined claims such as iss (issuer), exp (expiration time), sub (subject), aud (audience)</li>
                <li><strong>Public claims:</strong> Claims defined at will by those using JWTs</li>
                <li><strong>Private claims:</strong> Custom claims created to share information between parties</li>
            </ul>
            <div class="code-example bg-light p-3 rounded mb-3">
                <pre>{
  "sub": "1234567890",
  "name": "John Doe",
  "admin": true,
  "exp": 1516239022
}</pre>
            </div>

            <h3 class="mt-4">3. Signature</h3>
            <p>The signature is used to verify that the message wasn't changed along the way. To create the signature, you must take the encoded header, the encoded payload, a secret, and the algorithm specified in the header, and sign that.</p>
            <div class="code-example bg-light p-3 rounded mb-3">
                <pre>HMACSHA256(
  base64UrlEncode(header) + "." +
  base64UrlEncode(payload),
  secret
)</pre>
            </div>
        </section>

        <section class="mb-5">
            <h2>The Decoding Process</h2>
            <p>When our decoder receives a JWT, it performs the following steps:</p>
            <ol>
                <li><strong>Split the Token:</strong> The JWT is split into its three components at the dots</li>
                <li><strong>Base64URL Decode:</strong> Each part is decoded from Base64URL encoding</li>
                <li><strong>Parse JSON:</strong> The decoded header and payload are parsed as JSON</li>
                <li><strong>Validation (Optional):</strong> If a secret key is provided, the signature can be validated</li>
            </ol>
        </section>

        <section class="mb-5">
            <h2>Security Considerations</h2>
            <p>When working with JWTs, keep these security considerations in mind:</p>
            <ul>
                <li><strong>Never store sensitive information</strong> in the JWT payload as it can be decoded by anyone</li>
                <li>Always use <strong>HTTPS</strong> to transmit JWTs</li>
                <li>Set appropriate <strong>expiration times</strong> for your tokens</li>
                <li>Use strong secrets or keys for signing tokens</li>
                <li>Validate all claims according to your application's requirements</li>
            </ul>
        </section>

        <section class="mb-5">
            <h2>Common Use Cases</h2>
            <div class="use-cases">
                <h3>1. Authentication</h3>
                <p>The most common use case for JWTs is authentication. Once a user logs in, each subsequent request will include the JWT, allowing the user to access routes, services, and resources that are permitted with that token.</p>

                <h3>2. Information Exchange</h3>
                <p>JWTs can be used to securely transmit information between parties. Because JWTs can be signed, you can be sure the senders are who they say they are and that the content hasn't been tampered with.</p>

                <h3>3. Authorization</h3>
                <p>Once a user is logged in, an application can use JWT to manage authorization, controlling what actions the user can perform based on claims in the token.</p>
            </div>
        </section>

        <section class="mb-5">
            <h2>Best Practices for Implementation</h2>
            <div class="best-practices">
                <ul>
                    <li><strong>Token Storage:</strong> Store tokens securely using httpOnly cookies or secure local storage</li>
                    <li><strong>Token Size:</strong> Keep tokens small to minimize bandwidth impact</li>
                    <li><strong>Error Handling:</strong> Implement proper error handling for token validation failures</li>
                    <li><strong>Token Refresh:</strong> Implement a token refresh mechanism for long-lived sessions</li>
                    <li><strong>Monitoring:</strong> Log and monitor JWT usage for security and debugging</li>
                </ul>
            </div>
        </section>
    </article>

    <!-- Call to Action -->
    <div class="cta-section text-center py-5">
        <h2>Ready to Try JWT Decode?</h2>
        <p class="mb-4">Use our free online JWT decoder to analyze and validate your tokens.</p>
        <a href="/#decoder-tool" class="btn btn-primary btn-lg">Try JWT Decoder Now</a>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .how-it-works-content {
        max-width: 800px;
        margin: 0 auto;
    }
    .jwt-structure-visual {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
    }
    .code-example {
        font-family: monospace;
        white-space: pre-wrap;
    }
    .use-cases, .best-practices {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
    }
    .cta-section {
        background: #f8f9fa;
        border-radius: 8px;
        margin-top: 40px;
    }
</style>
{% endblock %} 