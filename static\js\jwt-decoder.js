/**
 * JWT Decoder JavaScript
 * Client-side JWT token decoding and display
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const jwtInput = document.getElementById('jwt-token-input');
    const decodeButton = document.getElementById('decode-button');
    const clearButton = document.getElementById('clear-button');
    const decodedOutput = document.getElementById('decoded-output');
    const headerJson = document.getElementById('header-json');
    const payloadJson = document.getElementById('payload-json');
    const signatureData = document.getElementById('signature-data');
    const signatureStatus = document.getElementById('signature-verification-status');
    const copyButtons = document.querySelectorAll('.copy-btn');

    // Syntax highlighting colors for JSON
    const syntaxColors = {
        key: '#e83e8c',
        string: '#28a745',
        number: '#007bff',
        boolean: '#ff6b6b',
        null: '#6c757d'
    };

    // Initialize tooltips using native browser tooltips (title attribute)
    document.querySelectorAll('[data-tippy-content]').forEach(element => {
        const content = element.getAttribute('data-tippy-content');
        if (content) {
            element.setAttribute('title', content);
        }
    });

    // Add dynamic header animation
    const jwtParts = document.querySelectorAll('.jwt-part');
    if (jwtParts.length) {
        jwtParts.forEach(part => {
            part.addEventListener('mouseenter', () => {
                part.style.transform = 'translateY(-8px)';
            });
            part.addEventListener('mouseleave', () => {
                part.style.transform = 'translateY(0)';
            });
        });
    }

    // Decode JWT token with enhanced error handling
    function decodeJWT(token) {
        try {
            // Clean up the token by removing any leading/trailing spaces
            token = token.trim();
            
            // Split the token
            const parts = token.split('.');
            
            if (parts.length !== 3) {
                throw new Error('Invalid JWT format. Expected 3 parts separated by dots.');
            }
            
            // Advanced validation of token format
            const headerPart = parts[0];
            const payloadPart = parts[1];
            const signaturePart = parts[2];
            
            if (!headerPart || !payloadPart || !signaturePart) {
                throw new Error('Invalid JWT: All parts must be non-empty');
            }
            
            // Helper function to handle base64 decoding with padding adjustment
            const base64UrlDecode = (str) => {
                // Replace URL-safe characters and add padding if necessary
                let padded = str.replace(/-/g, '+').replace(/_/g, '/');
                const padding = padded.length % 4;
                if (padding) {
                    padded += '='.repeat(4 - padding);
                }
                // Decode and convert to UTF-8 string
                try {
                    return JSON.parse(atob(padded));
                } catch (e) {
                    throw new Error(`Failed to parse JWT part: ${e.message}`);
                }
            };
            
            // Decode header and payload
            const header = base64UrlDecode(headerPart);
            const payload = base64UrlDecode(payloadPart);
            
            // Basic validation of decoded data
            if (!header || typeof header !== 'object') {
                throw new Error('Invalid JWT header: Must be a valid JSON object');
            }
            
            if (!payload || typeof payload !== 'object') {
                throw new Error('Invalid JWT payload: Must be a valid JSON object');
            }
            
            // Return decoded parts with additional metadata
            return {
                header,
                payload,
                signature: signaturePart,
                parts: { headerPart, payloadPart, signaturePart },
                valid: true,
                raw: token
            };
        } catch (error) {
            console.error('JWT decode error:', error);
            return {
                error: error.message,
                valid: false,
                raw: token || ''
            };
        }
    }

    // Format JSON with enhanced highlighting and readability
    function formatJSON(obj) {
        if (!obj) return '';
        
        // Use a more structured approach to JSON formatting
        const formattedJson = JSON.stringify(obj, null, 2)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, match => {
                let cls = 'json-number';
                let color = syntaxColors.number;
                
                if (/^"/.test(match)) {
                    if (/:$/.test(match)) {
                        cls = 'json-key';
                        color = syntaxColors.key;
                    } else {
                        cls = 'json-string';
                        color = syntaxColors.string;
                    }
                } else if (/true|false/.test(match)) {
                    cls = 'json-boolean';
                    color = syntaxColors.boolean;
                } else if (/null/.test(match)) {
                    cls = 'json-null';
                    color = syntaxColors.null;
                }
                
                return `<span class="${cls}" style="color: ${color}">${match}</span>`;
            });
            
        return formattedJson;
    }
    
    // Format the payload with time-related fields enhanced
    function formatPayloadWithTimeConversion(payload) {
        if (!payload) return '';
        
        // Create a copy to avoid modifying the original
        const payloadCopy = JSON.parse(JSON.stringify(payload));
        const timeFields = ['exp', 'iat', 'nbf', 'auth_time'];
        
        // Process the JSON and add comments for time fields
        let formattedJson = JSON.stringify(payloadCopy, null, 2);
        
        // Replace the time values with formatted time strings
        timeFields.forEach(field => {
            if (payloadCopy[field] && typeof payloadCopy[field] === 'number') {
                const date = new Date(payloadCopy[field] * 1000);
                const formattedDate = date.toLocaleString();
                
                // Use regex to find the specific field value in the JSON
                const regex = new RegExp(`"${field}":\\s*(\\d+)`, 'g');
                formattedJson = formattedJson.replace(regex, `"${field}": $1 /* ${formattedDate} */`);
            }
        });
        
        // Apply syntax highlighting
        return formattedJson
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?|\/\*.*?\*\/)/g, match => {
                let cls = 'json-number';
                let color = syntaxColors.number;
                
                if (/^\/\*/.test(match)) {
                    // Comment formatting
                    cls = 'json-comment';
                    color = '#6c757d'; // Gray color for comments
                } else if (/^"/.test(match)) {
                    if (/:$/.test(match)) {
                        cls = 'json-key';
                        color = syntaxColors.key;
                    } else {
                        cls = 'json-string';
                        color = syntaxColors.string;
                    }
                } else if (/true|false/.test(match)) {
                    cls = 'json-boolean';
                    color = syntaxColors.boolean;
                } else if (/null/.test(match)) {
                    cls = 'json-null';
                    color = syntaxColors.null;
                }
                
                return `<span class="${cls}" style="color: ${color}">${match}</span>`;
            });
    }

    // Display decoded JWT with animations and enhanced status info
    function displayDecodedJWT(decoded) {
        // Slide up animation for output container
        if (decodedOutput.style.display === 'none') {
            decodedOutput.style.display = 'block';
            decodedOutput.style.opacity = '0';
            decodedOutput.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                decodedOutput.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                decodedOutput.style.opacity = '1';
                decodedOutput.style.transform = 'translateY(0)';
            }, 10);
        }
        
        if (!decoded.valid) {
            // Show error message with helpful suggestions
            const errorMsg = `
                <div class="alert alert-danger">
                    <strong>Error:</strong> ${decoded.error}
                    <hr>
                    <small>Make sure your token follows the format: xxxxx.yyyyy.zzzzz</small>
                </div>
            `;
            headerJson.innerHTML = errorMsg;
            payloadJson.innerHTML = '';
            signatureData.innerHTML = '';
            signatureStatus.innerHTML = '<span class="badge bg-danger">Invalid Token</span>';
            return;
        }
        
        // Display header with animation
        headerJson.innerHTML = formatJSON(decoded.header);
        animateElement(headerJson);
        
        // Display payload with animation and time-field conversion
        payloadJson.innerHTML = formatPayloadWithTimeConversion(decoded.payload);
        animateElement(payloadJson);

        // Update claims count
        updatePayloadClaimsCount(decoded.payload);

        // Update JWT summary table
        updateJWTSummaryTable(decoded);
        
        // Display signature
        signatureData.textContent = decoded.signature;
        animateElement(signatureData);
        
        // Enhanced token verification status with more detailed information
        const currentTime = Math.floor(Date.now() / 1000);
        let statusHtml = '';
        
        // Check algorithm security
        if (decoded.header.alg === 'none' || decoded.header.alg === 'HS256') {
            statusHtml += '<span class="badge bg-warning me-2" title="HS256 and none are considered less secure algorithms">Algorithm: ' + decoded.header.alg + '</span>';
        } else {
            statusHtml += '<span class="badge bg-success me-2">Algorithm: ' + decoded.header.alg + '</span>';
        }
        
        // Check token expiration
        if (decoded.payload.exp) {
            const expiryDate = new Date(decoded.payload.exp * 1000);
            const timeRemaining = decoded.payload.exp - currentTime;
            
            if (timeRemaining < 0) {
                // Expired token
                statusHtml += '<span class="badge bg-danger me-2">Expired: ' + timeSince(expiryDate) + ' ago</span>';
            } else if (timeRemaining < 3600) {
                // Expiring soon (less than 1 hour)
                statusHtml += '<span class="badge bg-warning me-2">Expiring Soon: ' + expiryDate.toLocaleString() + '</span>';
            } else {
                // Valid token
                statusHtml += '<span class="badge bg-success me-2">Valid until: ' + expiryDate.toLocaleString() + '</span>';
            }
        } else {
            statusHtml += '<span class="badge bg-info me-2">No Expiration Date</span>';
        }
        
        // Check issued at time
        if (decoded.payload.iat) {
            const issuedDate = new Date(decoded.payload.iat * 1000);
            statusHtml += '<span class="badge bg-secondary me-2">Issued: ' + issuedDate.toLocaleString() + '</span>';
        }
        
        signatureStatus.innerHTML = statusHtml;
    }

    // Helper function to animate elements
    function animateElement(element) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(10px)';
        
        setTimeout(() => {
            element.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, 10);
    }
    
    // Helper function to format time since a date
    function timeSince(date) {
        const seconds = Math.floor((new Date() - date) / 1000);
        
        let interval = seconds / 31536000;
        if (interval > 1) return Math.floor(interval) + " years";
        
        interval = seconds / 2592000;
        if (interval > 1) return Math.floor(interval) + " months";
        
        interval = seconds / 86400;
        if (interval > 1) return Math.floor(interval) + " days";
        
        interval = seconds / 3600;
        if (interval > 1) return Math.floor(interval) + " hours";
        
        interval = seconds / 60;
        if (interval > 1) return Math.floor(interval) + " minutes";
        
        return Math.floor(seconds) + " seconds";
    }

    // Handle decode button click with improved feedback
    if (decodeButton) {
        decodeButton.addEventListener('click', () => {
            const token = jwtInput.value.trim();
            
            if (!token) {
                // Show temporary error message in the input area
                jwtInput.classList.add('is-invalid');
                // Add animated error message below the input
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback animate__animated animate__fadeIn';
                errorDiv.textContent = 'Please enter a JWT token to decode';
                jwtInput.parentNode.appendChild(errorDiv);
                
                // Remove error state after 3 seconds
                setTimeout(() => {
                    jwtInput.classList.remove('is-invalid');
                    if (errorDiv.parentNode) {
                        errorDiv.parentNode.removeChild(errorDiv);
                    }
                }, 3000);
                
                return;
            }
            
            // Add loading animation to button
            const originalText = decodeButton.innerHTML;
            decodeButton.disabled = true;
            decodeButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Decoding...';
            
            // Use setTimeout to give UI time to update before heavy processing
            setTimeout(() => {
                const decoded = decodeJWT(token);
                displayDecodedJWT(decoded);
                
                // Restore button state
                decodeButton.disabled = false;
                decodeButton.innerHTML = originalText;
                
                // Store in history if valid
                if (decoded.valid && window.localStorage) {
                    storeTokenInHistory(decoded.raw);
                }
            }, 300);
        });
    }

    // Store token in local history (for recent tokens feature)
    function storeTokenInHistory(token) {
        try {
            let tokenHistory = JSON.parse(localStorage.getItem('jwt_history') || '[]');
            // Don't store duplicates
            if (!tokenHistory.includes(token)) {
                // Limit to 5 most recent tokens
                tokenHistory.unshift(token);
                if (tokenHistory.length > 5) {
                    tokenHistory = tokenHistory.slice(0, 5);
                }
                localStorage.setItem('jwt_history', JSON.stringify(tokenHistory));
            }
        } catch (e) {
            console.warn('Could not store token in history', e);
        }
    }

    // Handle clear button click with animation
    if (clearButton) {
        clearButton.addEventListener('click', () => {
            // Flash the input field before clearing
            jwtInput.style.transition = 'background-color 0.3s ease';
            jwtInput.style.backgroundColor = 'rgba(220, 53, 69, 0.1)';
            
            setTimeout(() => {
                jwtInput.style.backgroundColor = '';
                jwtInput.value = '';
                
                // Animate output container disappearing
                if (decodedOutput.style.display !== 'none') {
                    decodedOutput.style.opacity = '0';
                    decodedOutput.style.transform = 'translateY(20px)';
                    
                    setTimeout(() => {
                        decodedOutput.style.display = 'none';
                    }, 300);
                }
            }, 300);
        });
    }

    // Handle copy buttons with improved feedback
    if (copyButtons) {
        copyButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetId = button.dataset.target;
                const targetElement = document.getElementById(targetId);
                
                if (!targetElement) return;
                
                // Text to copy - handle different format types
                let textToCopy;
                if (targetElement.tagName === 'PRE') {
                    try {
                        // For JSON content, get the raw text without formatting
                        if (targetElement.classList.contains('json-output')) {
                            const text = targetElement.textContent.trim();
                            // Try to parse as JSON to ensure we get clean JSON output
                            textToCopy = JSON.stringify(JSON.parse(text), null, 2);
                        } else {
                            textToCopy = targetElement.textContent;
                        }
                    } catch (e) {
                        textToCopy = targetElement.textContent;
                    }
                } else {
                    // For regular text elements
                    textToCopy = targetElement.textContent;
                }
                
                // Copy to clipboard with enhanced error handling
                if (!navigator.clipboard) {
                    // Fallback for older browsers
                    const textarea = document.createElement('textarea');
                    textarea.value = textToCopy;
                    textarea.style.position = 'fixed'; // Avoid scrolling to bottom
                    document.body.appendChild(textarea);
                    textarea.focus();
                    textarea.select();
                    
                    try {
                        const successful = document.execCommand('copy');
                        if (successful) {
                            showCopyFeedback(button, true);
                        } else {
                            showCopyFeedback(button, false);
                        }
                    } catch (err) {
                        console.error('Fallback: Could not copy text: ', err);
                        showCopyFeedback(button, false);
                    }
                    
                    document.body.removeChild(textarea);
                } else {
                    // Modern clipboard API
                    navigator.clipboard.writeText(textToCopy)
                        .then(() => {
                            showCopyFeedback(button, true);
                        })
                        .catch(err => {
                            console.error('Failed to copy text: ', err);
                            showCopyFeedback(button, false);
                        });
                }
            });
        });
    }
    
    // Show copy feedback animation
    function showCopyFeedback(button, success) {
        const originalText = button.innerHTML;
        const originalClass = button.className;
        
        if (success) {
            button.innerHTML = '<i class="fas fa-check"></i> Copied!';
            button.classList.add('copied');
        } else {
            button.innerHTML = '<i class="fas fa-times"></i> Failed';
            button.classList.add('btn-danger');
        }
        
        // Subtle success animation
        button.style.transform = 'scale(1.1)';
        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 200);
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.className = originalClass;
        }, 2000);
    }

    // Auto-decode if token is in URL hash or params
    function checkUrlForToken() {
        let token = null;
        
        // Check for token in hash
        if (window.location.hash && window.location.hash.length > 1) {
            const hash = window.location.hash.substring(1);
            if (hash && hash.includes('.') && hash.split('.').length === 3) {
                token = hash;
            }
        }
        
        // Check for token in query params
        if (!token) {
            const urlParams = new URLSearchParams(window.location.search);
            const tokenParam = urlParams.get('token');
            if (tokenParam && tokenParam.includes('.') && tokenParam.split('.').length === 3) {
                token = tokenParam;
            }
        }
        
        // If token is found, decode it
        if (token) {
            jwtInput.value = token;
            // Trigger decoding with a slight delay to ensure the page is fully loaded
            setTimeout(() => {
                decodeButton.click();
            }, 500);
        }
    }

    // Check for token in URL when page loads
    checkUrlForToken();
    
    // Then set default token if no token was found in URL
    setDefaultTokenAndDecode();

    // Add input event for live decoding with improved debouncing
    if (jwtInput) {
        let debounceTimer;
        
        jwtInput.addEventListener('input', () => {
            const token = jwtInput.value.trim();
            
            // Clear any previous timer
            clearTimeout(debounceTimer);
            
            // Hide the decoded output until we have a valid token
            if (!token || token.split('.').length !== 3) {
                if (decodedOutput.style.display !== 'none') {
                    decodedOutput.style.opacity = '0';
                    decodedOutput.style.transform = 'translateY(20px)';
                    
                    setTimeout(() => {
                        decodedOutput.style.display = 'none';
                    }, 300);
                }
                return;
            }
            
            // Add a subtle highlight effect to indicate processing
            jwtInput.style.borderColor = '#8e54e9';
            
            // Set a new timer for debouncing
            debounceTimer = setTimeout(() => {
                jwtInput.style.borderColor = '';
                const decoded = decodeJWT(token);
                displayDecodedJWT(decoded);
            }, 500);
        });
        
        // Focus the input field on page load for better UX
        setTimeout(() => {
            jwtInput.focus();
        }, 500);
    }
    
    // Initialize keyboard shortcuts for better accessibility
    document.addEventListener('keydown', (e) => {
        // Alt+D to focus decode button
        if (e.altKey && e.key === 'd' && decodeButton) {
            e.preventDefault();
            decodeButton.click();
        }
        
        // Alt+C to focus clear button
        if (e.altKey && e.key === 'c' && clearButton) {
            e.preventDefault();
            clearButton.click();
        }
        
        // Ctrl+Enter to decode
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter' && decodeButton) {
            e.preventDefault();
            decodeButton.click();
        }
    });

    // Handle share button click
    const shareTokenBtn = document.getElementById('share-token-btn');
    const shareModalElement = document.getElementById('shareModal');
    const shareModal = shareModalElement ? new bootstrap.Modal(shareModalElement, {}) : null;
    const shareLink = document.getElementById('share-link');
    const copyShareLink = document.getElementById('copy-share-link');
    
    if (shareTokenBtn && shareLink && copyShareLink && shareModal) {
        shareTokenBtn.addEventListener('click', () => {
            const token = jwtInput.value.trim();
            if (!token) return;
            
            // Create a shareable URL with the token as a hash parameter
            const url = new URL(window.location.href);
            url.hash = token;
            shareLink.value = url.toString();
            
            // Show the modal
            shareModal.show();
        });
        
        // Handle copy share link button
        copyShareLink.addEventListener('click', () => {
            shareLink.select();
            navigator.clipboard.writeText(shareLink.value)
                .then(() => {
                    // Show success feedback
                    const originalText = copyShareLink.innerHTML;
                    copyShareLink.innerHTML = '<i class="fas fa-check"></i> Copied!';
                    
                    setTimeout(() => {
                        copyShareLink.innerHTML = originalText;
                    }, 2000);
                })
                .catch(err => {
                    console.error('Failed to copy link: ', err);
                });
        });
    }
    
    // Handle download JSON button
    const downloadJsonBtn = document.getElementById('download-json-btn');
    if (downloadJsonBtn) {
        downloadJsonBtn.addEventListener('click', (e) => {
            e.preventDefault();
            
            // Get the current decoded token
            const token = jwtInput.value.trim();
            if (!token) return;
            
            try {
                const decoded = decodeJWT(token);
                if (!decoded.valid) return;
                
                // Prepare the JSON data
                const jsonData = {
                    header: decoded.header,
                    payload: decoded.payload,
                    signature: decoded.signature,
                    token: token
                };
                
                // Create a Blob with the JSON data
                const blob = new Blob([JSON.stringify(jsonData, null, 2)], { type: 'application/json' });
                
                // Create a download link and trigger a click
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `jwt-token-${new Date().getTime()}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            } catch (err) {
                console.error('Failed to download JSON: ', err);
            }
        });
    }

    // Update payload claims count
    function updatePayloadClaimsCount(payload) {
        const claimsCountElement = document.getElementById('payload-claims-count');
        if (claimsCountElement && payload) {
            const claimsCount = Object.keys(payload).length;
            claimsCountElement.textContent = `${claimsCount} claim${claimsCount !== 1 ? 's' : ''}`;
        }
    }

    // Update JWT summary table
    function updateJWTSummaryTable(decoded) {
        if (!decoded.valid) return;

        const { header, payload } = decoded;

        // Helper function to safely get value or return '-'
        const getValue = (obj, key) => obj && obj[key] !== undefined ? obj[key] : '-';

        // Helper function to format timestamp
        const formatTimestamp = (timestamp) => {
            if (!timestamp || timestamp === '-') return { timestamp: '-', readable: '' };
            try {
                const date = new Date(timestamp * 1000);
                return {
                    timestamp: timestamp,
                    readable: date.toLocaleString()
                };
            } catch (e) {
                return { timestamp: timestamp, readable: '' };
            }
        };

        // Update algorithm
        const algorithmElement = document.getElementById('summary-algorithm');
        if (algorithmElement) {
            algorithmElement.textContent = getValue(header, 'alg');
        }

        // Update token type
        const typeElement = document.getElementById('summary-type');
        if (typeElement) {
            typeElement.textContent = getValue(header, 'typ');
        }

        // Update issuer
        const issuerElement = document.getElementById('summary-issuer');
        if (issuerElement) {
            issuerElement.textContent = getValue(payload, 'iss');
        }

        // Update subject
        const subjectElement = document.getElementById('summary-subject');
        if (subjectElement) {
            subjectElement.textContent = getValue(payload, 'sub');
        }

        // Update issued at
        const issuedTimestamp = getValue(payload, 'iat');
        const issuedFormatted = formatTimestamp(issuedTimestamp);
        const issuedTimestampElement = document.getElementById('summary-issued-timestamp');
        const issuedReadableElement = document.getElementById('summary-issued-readable');
        if (issuedTimestampElement) {
            issuedTimestampElement.textContent = issuedFormatted.timestamp;
        }
        if (issuedReadableElement) {
            issuedReadableElement.textContent = issuedFormatted.readable;
        }

        // Update expiration
        const expirationTimestamp = getValue(payload, 'exp');
        const expirationFormatted = formatTimestamp(expirationTimestamp);
        const expirationTimestampElement = document.getElementById('summary-expiration-timestamp');
        const expirationReadableElement = document.getElementById('summary-expiration-readable');
        if (expirationTimestampElement) {
            expirationTimestampElement.textContent = expirationFormatted.timestamp;
        }
        if (expirationReadableElement) {
            expirationReadableElement.textContent = expirationFormatted.readable;
        }

        // Update audience
        const audienceElement = document.getElementById('summary-audience');
        if (audienceElement) {
            const audience = getValue(payload, 'aud');
            // Handle array audience
            if (Array.isArray(audience)) {
                audienceElement.textContent = audience.join(', ');
            } else {
                audienceElement.textContent = audience;
            }
        }
    }

    // Adding a default token and auto-decode functionality
    function setDefaultTokenAndDecode() {
        // Default sample JWT token with typical fields
        const defaultToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************.qjPT81beX_rx92bcpuyXavKYUfZHG7cJ_XHvw5AGt2g';
        
        if (jwtInput && !jwtInput.value.trim()) {
            // Set the default token in the input field
            jwtInput.value = defaultToken;
            
            // Trigger decoding - slight delay to ensure DOM is ready
            setTimeout(() => {
                if (decodeButton) {
                    decodeButton.click();
                } else {
                    // If button is not available, decode directly
                    const decoded = decodeJWT(defaultToken);
                    displayDecodedJWT(decoded);
                }
            }, 500);
        }
    }
}); 