/* 理想体重计算器样式 */
.ideal-weight-calculator {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.ideal-weight-calculator h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 600;
}

.btn-calculate {
    background-color: #3498db;
    color: white;
    padding: 10px 25px;
    border: none;
    border-radius: 5px;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.btn-calculate:hover {
    background-color: #2980b9;
    color: white;
}

.ideal-weight-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #3498db;
    margin: 15px 0 5px;
}

.ideal-weight-range {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-bottom: 20px;
}

.weight-scale {
    height: 20px;
    background: linear-gradient(to right, #e74c3c 0%, #e74c3c 30%, #2ecc71 30%, #2ecc71 70%, #e74c3c 70%, #e74c3c 100%);
    border-radius: 10px;
    position: relative;
    margin-bottom: 10px;
}

.weight-marker {
    width: 15px;
    height: 30px;
    background-color: #2c3e50;
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 3px;
}

.weight-scale-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #7f8c8d;
}

.weight-recommendation {
    background-color: #eaf2f8;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.weight-recommendation h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.formula-box {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

@media (max-width: 768px) {
    .ideal-weight-calculator {
        padding: 15px;
    }
    
    .ideal-weight-value {
        font-size: 2rem;
    }
    
    .ideal-weight-range {
        font-size: 1rem;
    }
}

/* 内容区域样式 */
.content-section {
    margin-top: 40px;
}

.content-section h2,
.content-section h3,
.content-section h4 {
    color: #2c3e50;
    margin-top: 30px;
    margin-bottom: 15px;
}

.content-section p {
    color: #34495e;
    line-height: 1.6;
    margin-bottom: 15px;
}

.content-section ul {
    padding-left: 20px;
    margin-bottom: 20px;
    color: #34495e;
}

.content-section ul li {
    margin-bottom: 8px;
}

.sidebar {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 40px;
}

.sidebar h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.sidebar .list-group-item {
    background-color: transparent;
    border-left: 0;
    border-right: 0;
    border-radius: 0;
    padding: 12px 15px;
}

.sidebar .list-group-item a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.2s ease;
}

.sidebar .list-group-item a:hover {
    color: #2980b9;
    text-decoration: underline;
} 