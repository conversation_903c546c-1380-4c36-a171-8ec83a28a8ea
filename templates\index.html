{% extends "base.html" %}

{% block title %}JWT Decode Online | Free JWT Token Decoder Tool{% endblock %}

{% block description %}Decode JWT tokens instantly with our free, secure online JWT decoder. Simple interface, instant results, 100% client-side processing. No installation required.{% endblock %}

{% block meta_keywords %}jwt decode, jwt decode online, JWT token decoder, decode jwt token, jwt parser, json web token decoder, jwt debugger{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/jwt-decoder.css') }}" rel="stylesheet">
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "JWT Decode Online",
  "description": "Free online JWT token decoder tool. Decode JSON Web Tokens instantly in your browser with 100% client-side processing.",
  "url": "https://jwtdecode.online",
  "applicationCategory": "DeveloperApplication",
  "operatingSystem": "Any",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD"
  },
  "featureList": [
    "JWT Token Decoding",
    "Client-side Processing",
    "No Installation Required",
    "Mobile Friendly",
    "Instant Results"
  ]
}
</script>
{% endblock %}

{% block content %}
<!-- Main Decoder Tool Section -->
<section class="decoder-section py-4">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-10">
                <div class="main-decoder-card">
                    <div class="decoder-header text-center mb-4">
                        <h1 class="decoder-main-title">JWT Token Decoder</h1>
                        <p class="decoder-subtitle">Paste your JWT token below and decode it instantly</p>
                    </div>

                    <div class="decoder-input-section mb-4">
                        <div class="input-wrapper">
                            <label for="jwt-token-input" class="input-label">
                                <i class="fas fa-key me-2"></i>JWT Token Input
                            </label>
                            <textarea
                                id="jwt-token-input"
                                class="jwt-input"
                                placeholder="Paste your JWT token here (e.g., eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c)"
                                rows="6"
                                spellcheck="false"
                            ></textarea>
                            <div class="input-actions mt-3">
                                <button id="decode-button" class="btn btn-decode">
                                    <i class="fas fa-unlock me-2"></i> Decode JWT Token
                                </button>
                                <button id="clear-button" class="btn btn-clear">
                                    <i class="fas fa-eraser me-2"></i> Clear
                                </button>
                                <div class="input-info">
                                    <small class="text-muted">
                                        <i class="fas fa-shield-alt me-1"></i>
                                        All processing happens in your browser - your tokens never leave your device
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Decoder Output -->
                    <div id="decoded-output" class="decoder-results" style="display: none;">
                        <div class="results-header mb-4">
                            <h2 class="results-title">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                Decoded JWT Token
                            </h2>
                        </div>

                        <!-- JWT Summary Table -->
                        <div class="jwt-summary-table mb-4">
                            <h3 class="summary-title">
                                <i class="fas fa-info-circle me-2"></i>JWT Token Summary
                            </h3>
                            <div class="table-responsive">
                                <table class="table jwt-info-table">
                                    <tbody>
                                        <tr>
                                            <td class="info-label">Algorithm/alg:</td>
                                            <td class="info-value" id="summary-algorithm">-</td>
                                        </tr>
                                        <tr>
                                            <td class="info-label">Token Type/typ:</td>
                                            <td class="info-value" id="summary-type">-</td>
                                        </tr>
                                        <tr>
                                            <td class="info-label">JWT Issuer/iss:</td>
                                            <td class="info-value" id="summary-issuer">-</td>
                                        </tr>
                                        <tr>
                                            <td class="info-label">Subject/sub:</td>
                                            <td class="info-value" id="summary-subject">-</td>
                                        </tr>
                                        <tr>
                                            <td class="info-label">Issued At/iat:</td>
                                            <td class="info-value">
                                                <span id="summary-issued-timestamp">-</span>
                                                <span class="timestamp-readable" id="summary-issued-readable"></span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="info-label">Expiration/exp:</td>
                                            <td class="info-value">
                                                <span id="summary-expiration-timestamp">-</span>
                                                <span class="timestamp-readable" id="summary-expiration-readable"></span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="info-label">Audience/aud:</td>
                                            <td class="info-value" id="summary-audience">-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="row g-4">
                            <!-- Header Section -->
                            <div class="col-lg-4">
                                <div class="result-card header-card">
                                    <div class="result-card-header">
                                        <div class="card-title-wrapper">
                                            <h3 class="card-title">
                                                <i class="fas fa-cog me-2"></i>Header
                                            </h3>
                                            <button class="copy-btn" data-target="header-json" title="Copy header">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                        <p class="card-description">Algorithm and token type</p>
                                    </div>
                                    <div class="result-card-body">
                                        <pre id="header-json" class="json-display"></pre>
                                    </div>
                                </div>
                            </div>

                            <!-- Payload Section -->
                            <div class="col-lg-4">
                                <div class="result-card payload-card">
                                    <div class="result-card-header">
                                        <div class="card-title-wrapper">
                                            <h3 class="card-title">
                                                <i class="fas fa-database me-2"></i>Payload
                                            </h3>
                                            <button class="copy-btn" data-target="payload-json" title="Copy payload">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                        <p class="card-description">
                                            Claims and user data
                                            <span id="payload-claims-count" class="claims-badge">0 claims</span>
                                        </p>
                                    </div>
                                    <div class="result-card-body">
                                        <pre id="payload-json" class="json-display"></pre>
                                    </div>
                                </div>
                            </div>

                            <!-- Signature Section -->
                            <div class="col-lg-4">
                                <div class="result-card signature-card">
                                    <div class="result-card-header">
                                        <div class="card-title-wrapper">
                                            <h3 class="card-title">
                                                <i class="fas fa-shield-alt me-2"></i>Signature
                                            </h3>
                                            <button class="copy-btn" data-target="signature-data" title="Copy signature">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                        <p class="card-description">Token verification</p>
                                    </div>
                                    <div class="result-card-body">
                                        <div id="signature-verification-status" class="verification-status mb-3"></div>
                                        <div id="signature-data" class="signature-display"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="results-actions mt-4">
                            <div class="d-flex flex-wrap justify-content-center gap-3">
                                <button id="share-token-btn" class="action-btn">
                                    <i class="fas fa-share-alt me-2"></i> Share Token
                                </button>
                                <a id="download-json-btn" href="#" class="action-btn">
                                    <i class="fas fa-download me-2"></i> Download JSON
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Info Section -->
<section class="quick-info py-4 bg-light">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-4 mb-3">
                <div class="info-item">
                    <i class="fas fa-shield-alt text-success mb-2"></i>
                    <h5>100% Secure</h5>
                    <p class="small text-muted">Client-side processing only</p>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="info-item">
                    <i class="fas fa-bolt text-warning mb-2"></i>
                    <h5>Instant Results</h5>
                    <p class="small text-muted">Decode tokens immediately</p>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="info-item">
                    <i class="fas fa-mobile-alt text-info mb-2"></i>
                    <h5>Mobile Friendly</h5>
                    <p class="small text-muted">Works on all devices</p>
                </div>
            </div>
        </div>
    </div>
</section>


<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <h2 class="text-center mb-5">Why Choose Our JWT Decoder?</h2>

        <div class="row">
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="feature-box text-center">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>100% Secure</h3>
                    <p>All processing happens in your browser. Your tokens never leave your device.</p>
                </div>
            </div>

            <div class="col-md-6 col-lg-3 mb-4">
                <div class="feature-box text-center">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3>Instant Results</h3>
                    <p>Decode JWT tokens instantly with no delays or waiting time.</p>
                </div>
            </div>

            <div class="col-md-6 col-lg-3 mb-4">
                <div class="feature-box text-center">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Mobile Friendly</h3>
                    <p>Works perfectly on all devices - desktop, tablet, and mobile.</p>
                </div>
            </div>

            <div class="col-md-6 col-lg-3 mb-4">
                <div class="feature-box text-center">
                    <div class="feature-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>Developer Friendly</h3>
                    <p>Clean, readable output perfect for debugging and development.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Additional Tools Section -->
<section class="tools-section py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">More JWT Tools</h2>

        <div class="row justify-content-center">
            <div class="col-md-4 mb-4">
                <div class="tool-card text-center">
                    <div class="tool-icon">
                        <i class="fas fa-check-circle fa-3x text-primary mb-3"></i>
                    </div>
                    <h3>JWT Validation</h3>
                    <p>Validate JWT tokens and verify their signatures</p>
                    <a href="/tools/jwt-validation" class="btn btn-outline-primary">Try Now</a>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="tool-card text-center">
                    <div class="tool-icon">
                        <i class="fas fa-cog fa-3x text-primary mb-3"></i>
                    </div>
                    <h3>JWT Generator</h3>
                    <p>Create and sign JWT tokens for testing</p>
                    <a href="/tools/jwt-generator" class="btn btn-outline-primary">Try Now</a>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="tool-card text-center">
                    <div class="tool-icon">
                        <i class="fas fa-download fa-3x text-primary mb-3"></i>
                    </div>
                    <h3>Offline Version</h3>
                    <p>Download our offline JWT decoder tool</p>
                    <a href="/tools/jwt-decode-offline" class="btn btn-outline-primary">Download</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq-section py-5">
    <div class="container">
        <h2 class="text-center mb-5">Frequently Asked Questions</h2>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                What is a JWT token?
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                JWT (JSON Web Token) is a compact, URL-safe means of representing claims between two parties. It consists of three parts: header, payload, and signature, separated by dots.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                Is it safe to decode JWT tokens online?
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes, our JWT decoder is completely safe. All processing happens in your browser (client-side), so your tokens never leave your device or get sent to our servers.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq3">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                Can I decode expired JWT tokens?
                            </button>
                        </h2>
                        <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes, you can decode any JWT token regardless of its expiration status. Our decoder simply parses the token structure and displays the contents.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Share Token Modal -->
<div class="modal fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="shareModalLabel">Share This JWT Token</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Use this link to share the token with others:</p>
                <div class="input-group mb-3">
                    <input type="text" id="share-link" class="form-control" readonly>
                    <button id="copy-share-link" class="btn btn-outline-primary" type="button">Copy</button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/jwt-decoder.js') }}"></script>
{% endblock %} 