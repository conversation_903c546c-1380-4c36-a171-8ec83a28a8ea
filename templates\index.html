{% extends "base.html" %}

{% block title %}JWT Decode Online | Free JWT Token Decoder Tool{% endblock %}

{% block description %}Decode JWT tokens instantly with our free online JWT decoder. Support for JavaScript, Flutter, Java, React, and more. No installation required.{% endblock %}

{% block meta_keywords %}jwt decode, jwt decode online, JWT token, jwt decoder, decode jwt{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
<link href="{{ url_for('static', filename='css/jwt-decoder.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section text-center py-5">
    <div class="container">
        <h1 class="hero-title mb-3"><span class="text-primary-light">JWT</span> <span class="text-highlight">Decode</span> <span class="text-primary-light">Online:</span> Instant Token Decoder</h1>
        <p class="hero-subtitle mb-4">Free, secure, and instant JWT token decoding right in your browser</p>
        <a href="#decoder-tool" class="btn btn-lg btn-primary animate__animated animate__pulse animate__infinite animate__slower">
            <i class="fas fa-key me-2"></i>Decode Your JWT Now
        </a>
    </div>
</section>

<!-- Main Decoder Tool Section -->
<section id="decoder-tool" class="py-5">
    <div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
                <div class="decoder-card">
                    <h2 class="decoder-title">Paste JWT Token Here</h2>
                    
                    <div class="form-group mb-4">
                        <textarea id="jwt-token-input" class="form-control" placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c" rows="4"></textarea>
                        <div class="token-info text-muted mt-1 small">
                            <i class="fas fa-info-circle"></i> Enter your JWT token above to decode it instantly
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="keyboard-shortcuts small text-muted">
                                <span data-tippy-content="Press Ctrl+Enter to decode"><i class="fas fa-keyboard me-1"></i> Shortcuts: Ctrl+Enter to decode</span>
                            </div>
                            <div class="action-buttons">
                                <button id="decode-button" class="btn btn-primary" data-tippy-content="Decode the JWT token">
                                    <i class="fas fa-key me-1"></i> Decode
                                </button>
                                <button id="clear-button" class="btn btn-outline-secondary ms-2" data-tippy-content="Clear the input and results">
                                    <i class="fas fa-times me-1"></i> Clear
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Decoder Output -->
                    <div id="decoded-output" class="decoder-output" style="display: none;">
                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h3 class="mb-0">Header</h3>
                                        <button class="btn btn-sm btn-outline-primary copy-btn" data-target="header-json" data-tippy-content="Copy header to clipboard">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <pre id="header-json" class="json-output language-json rounded-3"></pre>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-12 mb-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h3 class="mb-0">Payload</h3>
                                        <div>
                                            <span id="payload-claims-count" class="badge bg-info me-2">0 claims</span>
                                            <button class="btn btn-sm btn-outline-primary copy-btn" data-target="payload-json" data-tippy-content="Copy payload to clipboard">
                                                <i class="fas fa-copy"></i> Copy
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <pre id="payload-json" class="json-output language-json rounded-3"></pre>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-12 mb-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h3 class="mb-0">Signature</h3>
                                        <button class="btn btn-sm btn-outline-primary copy-btn" data-target="signature-data" data-tippy-content="Copy signature to clipboard">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <p id="signature-verification-status" class="mb-3"></p>
                                        <div id="signature-data" class="signature-text"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="token-actions d-flex flex-wrap justify-content-end gap-2">
                                    <button id="share-token-btn" class="btn btn-sm btn-outline-primary" data-tippy-content="Get shareable link">
                                        <i class="fas fa-share-alt me-1"></i> Share
                            </button>
                                    <a id="download-json-btn" href="#" class="btn btn-sm btn-outline-primary" data-tippy-content="Download as JSON file">
                                        <i class="fas fa-download me-1"></i> Download JSON
                                    </a>
                        </div>
                    </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="how-it-works py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">How JWT Decode Works</h2>
        
        <div class="row align-items-center mb-5">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <div class="jwt-structure-visual">
                    <div class="jwt-part header">
                        <div class="jwt-part-label">HEADER</div>
                        <div class="jwt-part-content">{"alg": "HS256", "typ": "JWT"}</div>
                                </div>
                    <div class="jwt-dot">.</div>
                    <div class="jwt-part payload">
                        <div class="jwt-part-label">PAYLOAD</div>
                        <div class="jwt-part-content">{"sub": "1234567890", "name": "John Doe"}</div>
                                </div>
                    <div class="jwt-dot">.</div>
                    <div class="jwt-part signature">
                        <div class="jwt-part-label">SIGNATURE</div>
                        <div class="jwt-part-content">HMACSHA256(base64UrlEncode(header) + "." + base64UrlEncode(payload), secret)</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <h3 class="mb-3">Understanding JWT Structure</h3>
                <p>JSON Web Tokens consist of three parts separated by dots (.), which are:</p>
                <ul class="feature-list">
                    <li><strong>Header</strong> - Contains the token type and signing algorithm</li>
                    <li><strong>Payload</strong> - Contains the claims (user data and metadata)</li>
                    <li><strong>Signature</strong> - Verifies the token hasn't been altered</li>
                </ul>
                <p class="mt-3">Our decoder splits these parts and displays them in a readable format. All processing happens client-side - your tokens never leave your browser.</p>
                <div class="mt-4">
                    <a href="/how-it-works" class="btn btn-outline-primary">Learn More <i class="fas fa-arrow-right ms-1"></i></a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Platform Specific Guides Section -->
<section class="platform-guides py-5">
    <div class="container">
        <h2 class="text-center mb-5">JWT Decode Across Platforms</h2>
        
        <div class="row">
            <!-- JavaScript -->
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="platform-card">
                    <div class="platform-icon">
                        <i class="fab fa-js"></i>
                    </div>
                    <h3>JavaScript</h3>
                    <div class="code-snippet">
                        <pre><code>// Using npm package
import jwtDecode from 'jwt-decode';
const decoded = jwtDecode(token);</code></pre>
                    </div>
                       </div>
                            </div>
            
            <!-- Flutter -->
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="platform-card">
                    <div class="platform-icon">
                        <i class="fab fa-flutter"></i>
                    </div>
                    <h3>Flutter</h3>
                    <div class="code-snippet">
                        <pre><code>// Using dart package
import 'package:jwt_decoder/jwt_decoder.dart';
Map<String, dynamic> decodedToken = JwtDecoder.decode(token);</code></pre>
                    </div>
                    </div>
            </div>
            
            <!-- Java -->
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="platform-card">
                    <div class="platform-icon">
                        <i class="fab fa-java"></i>
                    </div>
                    <h3>Java</h3>
                    <div class="code-snippet">
                        <pre><code>// Using JJWT library
Jws<Claims> jwsClaims = Jwts.parserBuilder()
    .setSigningKey(key)
    .build()
    .parseClaimsJws(token);</code></pre>
                    </div>
                   </div>
            </div>
            
            <!-- Command Line -->
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="platform-card">
                    <div class="platform-icon">
                        <i class="fas fa-terminal"></i>
                    </div>
                    <h3>Command Line</h3>
                    <div class="code-snippet">
                        <pre><code>// Using Node.js CLI
$ npx jwt-cli decode &lt;token&gt;

// Using jq
$ echo $TOKEN | cut -d. -f2 | base64 -d | jq</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
        
<!-- Features Section -->
<section class="features-section py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">Key Features & Benefits</h2>
        
        <div class="row">
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="feature-box text-center">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3>No Installation Required</h3>
                    <p>Decode tokens instantly in your browser with no downloads or installations.</p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="feature-box text-center">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>100% Client-Side</h3>
                    <p>All processing happens in your browser. Your tokens never leave your device.</p>
                </div>
                        </div>
        
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="feature-box text-center">
                    <div class="feature-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3>All JWT Formats</h3>
                    <p>Support for all JWT formats and algorithms including HS256, RS256, ES256.</p>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="feature-box text-center">
                    <div class="feature-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3>Advanced Options</h3>
                    <p>Validation, verification, and custom formatting options for power users.</p>
        </div>
            </div>
        </div>
    </div>
</section>

<!-- Popular Articles Section -->
<section class="articles-section py-5">
    <div class="container">
        <h2 class="text-center mb-5">Popular Articles</h2>
        
        <div class="row">
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="article-card">
                    <div class="article-icon text-center py-4">
                        <i class="fab fa-react fa-4x text-primary"></i>
                    </div>
                    <div class="article-content">
                        <h3>Understanding JWT in React Applications</h3>
                        <p>Learn how to implement JWT authentication in React apps for secure user sessions.</p>
                        <a href="/blog/jwt-in-react-applications" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                    </div>
                </div>
    </div>

            <div class="col-md-6 col-lg-3 mb-4">
                <div class="article-card">
                    <div class="article-icon text-center py-4">
                        <i class="fab fa-flutter fa-4x text-primary"></i>
                    </div>
                    <div class="article-content">
                        <h3>JWT Authentication in Flutter Apps</h3>
                        <p>Step-by-step guide to implementing JWT authentication in your Flutter applications.</p>
                        <a href="/blog/jwt-authentication-flutter" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="article-card">
                    <div class="article-icon text-center py-4">
                        <i class="fas fa-terminal fa-4x text-primary"></i>
                    </div>
                    <div class="article-content">
                        <h3>Command Line JWT Tools for Developers</h3>
                        <p>Boost your productivity with these powerful command-line tools for JWT management.</p>
                        <a href="/blog/command-line-jwt-tools" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                        </div>
                    </div>
                </div>
                
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="article-card">
                    <div class="article-icon text-center py-4">
                        <i class="fas fa-shield-alt fa-4x text-primary"></i>
                    </div>
                    <div class="article-content">
                        <h3>JWT vs Session Tokens: Which to Choose?</h3>
                        <p>A detailed comparison of JWT and session-based authentication approaches.</p>
                        <a href="/blog/jwt-vs-session-tokens" class="btn btn-link px-0">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="newsletter-section py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h2 class="mb-3">Get JWT Security Tips and Updates</h2>
                <p class="mb-4">Subscribe to our newsletter for the latest security best practices and JWT developments.</p>
                <form class="newsletter-form">
                    <div class="input-group mb-3">
                        <input type="email" class="form-control" placeholder="Your email address" aria-label="Email address">
                        <button class="btn btn-primary" type="submit">Subscribe</button>
                </div>
                    <div class="form-text text-muted">We respect your privacy. No spam, ever.</div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Share Token Modal -->
<div class="modal fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="shareModalLabel">Share This JWT Token</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Use this link to share the token with others:</p>
                <div class="input-group mb-3">
                    <input type="text" id="share-link" class="form-control" readonly>
                    <button id="copy-share-link" class="btn btn-outline-primary" type="button">Copy</button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/jwt-decoder.js') }}"></script>
{% endblock %} 