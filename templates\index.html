{% extends "base.html" %}

{% block title %}JWT Decode Online | Free JWT Token Decoder Tool{% endblock %}

{% block description %}Decode JWT tokens instantly with our free, secure online JWT decoder. Simple interface, instant results, 100% client-side processing. No installation required.{% endblock %}

{% block meta_keywords %}jwt decode, jwt decode online, JWT token decoder, decode jwt token, jwt parser, json web token decoder, jwt debugger{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/jwt-decoder.css') }}" rel="stylesheet">
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "JWT Decode Online",
  "description": "Free online JWT token decoder tool. Decode JSON Web Tokens instantly in your browser with 100% client-side processing.",
  "url": "https://jwtdecode.online",
  "applicationCategory": "DeveloperApplication",
  "operatingSystem": "Any",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD"
  },
  "featureList": [
    "JWT Token Decoding",
    "Client-side Processing",
    "No Installation Required",
    "Mobile Friendly",
    "Instant Results"
  ]
}
</script>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section text-center py-5">
    <div class="container">
        <h1 class="hero-title mb-3">JWT Decode Online</h1>
        <p class="hero-subtitle mb-4">Simple, secure, and instant JWT token decoding in your browser</p>
        <a href="#decoder-tool" class="btn btn-lg btn-primary">
            <i class="fas fa-key me-2"></i>Start Decoding
        </a>
    </div>
</section>

<!-- Main Decoder Tool Section -->
<section id="decoder-tool" class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="decoder-card">
                    <h2 class="decoder-title text-center">JWT Token Decoder</h2>

                    <div class="form-group mb-4">
                        <label for="jwt-token-input" class="form-label">Paste your JWT token below:</label>
                        <textarea id="jwt-token-input" class="form-control" placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c" rows="4"></textarea>
                        <div class="text-center mt-3">
                            <button id="decode-button" class="btn btn-primary btn-lg me-2">
                                <i class="fas fa-key me-1"></i> Decode Token
                            </button>
                            <button id="clear-button" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Clear
                            </button>
                        </div>
                    </div>
                    
                    <!-- Decoder Output -->
                    <div id="decoded-output" class="decoder-output" style="display: none;">
                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h3 class="mb-0">Header</h3>
                                        <button class="btn btn-sm btn-outline-primary copy-btn" data-target="header-json" data-tippy-content="Copy header to clipboard">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <pre id="header-json" class="json-output language-json rounded-3"></pre>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-12 mb-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h3 class="mb-0">Payload</h3>
                                        <div>
                                            <span id="payload-claims-count" class="badge bg-info me-2">0 claims</span>
                                            <button class="btn btn-sm btn-outline-primary copy-btn" data-target="payload-json" data-tippy-content="Copy payload to clipboard">
                                                <i class="fas fa-copy"></i> Copy
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <pre id="payload-json" class="json-output language-json rounded-3"></pre>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-12 mb-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h3 class="mb-0">Signature</h3>
                                        <button class="btn btn-sm btn-outline-primary copy-btn" data-target="signature-data" data-tippy-content="Copy signature to clipboard">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <p id="signature-verification-status" class="mb-3"></p>
                                        <div id="signature-data" class="signature-text"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="token-actions d-flex flex-wrap justify-content-end gap-2">
                                    <button id="share-token-btn" class="btn btn-sm btn-outline-primary" data-tippy-content="Get shareable link">
                                        <i class="fas fa-share-alt me-1"></i> Share
                            </button>
                                    <a id="download-json-btn" href="#" class="btn btn-sm btn-outline-primary" data-tippy-content="Download as JSON file">
                                        <i class="fas fa-download me-1"></i> Download JSON
                                    </a>
                        </div>
                    </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="how-it-works py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">How It Works</h2>

        <div class="row text-center">
            <div class="col-md-4 mb-4">
                <div class="feature-box">
                    <div class="feature-icon">
                        <i class="fas fa-paste"></i>
                    </div>
                    <h3>1. Paste Token</h3>
                    <p>Simply paste your JWT token into the input field above</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="feature-box">
                    <div class="feature-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3>2. Instant Decode</h3>
                    <p>Our tool instantly decodes the token into readable JSON format</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="feature-box">
                    <div class="feature-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3>3. View Results</h3>
                    <p>See the header, payload, and signature in an organized display</p>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <p class="text-muted">All processing happens in your browser - your tokens never leave your device</p>
        </div>
    </div>
</section>


<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <h2 class="text-center mb-5">Why Choose Our JWT Decoder?</h2>

        <div class="row">
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="feature-box text-center">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>100% Secure</h3>
                    <p>All processing happens in your browser. Your tokens never leave your device.</p>
                </div>
            </div>

            <div class="col-md-6 col-lg-3 mb-4">
                <div class="feature-box text-center">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3>Instant Results</h3>
                    <p>Decode JWT tokens instantly with no delays or waiting time.</p>
                </div>
            </div>

            <div class="col-md-6 col-lg-3 mb-4">
                <div class="feature-box text-center">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Mobile Friendly</h3>
                    <p>Works perfectly on all devices - desktop, tablet, and mobile.</p>
                </div>
            </div>

            <div class="col-md-6 col-lg-3 mb-4">
                <div class="feature-box text-center">
                    <div class="feature-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>Developer Friendly</h3>
                    <p>Clean, readable output perfect for debugging and development.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Additional Tools Section -->
<section class="tools-section py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">More JWT Tools</h2>

        <div class="row justify-content-center">
            <div class="col-md-4 mb-4">
                <div class="tool-card text-center">
                    <div class="tool-icon">
                        <i class="fas fa-check-circle fa-3x text-primary mb-3"></i>
                    </div>
                    <h3>JWT Validation</h3>
                    <p>Validate JWT tokens and verify their signatures</p>
                    <a href="/tools/jwt-validation" class="btn btn-outline-primary">Try Now</a>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="tool-card text-center">
                    <div class="tool-icon">
                        <i class="fas fa-cog fa-3x text-primary mb-3"></i>
                    </div>
                    <h3>JWT Generator</h3>
                    <p>Create and sign JWT tokens for testing</p>
                    <a href="/tools/jwt-generator" class="btn btn-outline-primary">Try Now</a>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="tool-card text-center">
                    <div class="tool-icon">
                        <i class="fas fa-download fa-3x text-primary mb-3"></i>
                    </div>
                    <h3>Offline Version</h3>
                    <p>Download our offline JWT decoder tool</p>
                    <a href="/tools/jwt-decode-offline" class="btn btn-outline-primary">Download</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq-section py-5">
    <div class="container">
        <h2 class="text-center mb-5">Frequently Asked Questions</h2>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                What is a JWT token?
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                JWT (JSON Web Token) is a compact, URL-safe means of representing claims between two parties. It consists of three parts: header, payload, and signature, separated by dots.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                Is it safe to decode JWT tokens online?
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes, our JWT decoder is completely safe. All processing happens in your browser (client-side), so your tokens never leave your device or get sent to our servers.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq3">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                Can I decode expired JWT tokens?
                            </button>
                        </h2>
                        <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes, you can decode any JWT token regardless of its expiration status. Our decoder simply parses the token structure and displays the contents.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Share Token Modal -->
<div class="modal fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="shareModalLabel">Share This JWT Token</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Use this link to share the token with others:</p>
                <div class="input-group mb-3">
                    <input type="text" id="share-link" class="form-control" readonly>
                    <button id="copy-share-link" class="btn btn-outline-primary" type="button">Copy</button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/jwt-decoder.js') }}"></script>
{% endblock %} 