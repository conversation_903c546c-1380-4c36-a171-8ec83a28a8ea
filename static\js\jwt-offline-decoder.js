/**
 * JWT Offline Decoder JavaScript
 * 100% client-side JWT token decoding with enhanced visualization
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const jwtInput = document.getElementById('jwt-token-input');
    const decodeButton = document.getElementById('decode-button');
    const clearButton = document.getElementById('clear-button');
    const decodedOutput = document.getElementById('decoded-output');
    const headerJson = document.getElementById('header-json');
    const payloadJson = document.getElementById('payload-json');
    const signatureData = document.getElementById('signature-data');
    const signatureStatus = document.getElementById('signature-verification-status');
    const payloadClaimsCount = document.getElementById('payload-claims-count');
    const copyButtons = document.querySelectorAll('.copy-btn');
    
    // Token analysis elements
    const tokenAlgorithm = document.getElementById('token-algorithm');
    const tokenIssuedAt = document.getElementById('token-issued-at');
    const tokenExpiration = document.getElementById('token-expiration');
    const tokenStatus = document.getElementById('token-status');
    const tokenTimeline = document.getElementById('token-timeline');

    // Syntax highlighting colors for JSON
    const syntaxColors = {
        key: '#e83e8c',
        string: '#28a745',
        number: '#007bff',
        boolean: '#ff6b6b',
        null: '#6c757d',
        comment: '#6c757d'
    };

    // Event listeners
    decodeButton.addEventListener('click', () => {
        const token = jwtInput.value.trim();
        if (token) {
            processToken(token);
        }
    });

    clearButton.addEventListener('click', () => {
        jwtInput.value = '';
        decodedOutput.style.display = 'none';
        // Clear saved token from localStorage
        localStorage.removeItem('jwt_offline_decoder_last_token');
    });

    jwtInput.addEventListener('keydown', (e) => {
        // Ctrl+Enter to decode
        if (e.ctrlKey && e.key === 'Enter') {
            decodeButton.click();
        }
    });

    // Copy button functionality
    copyButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetId = button.getAttribute('data-target');
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                let textToCopy = '';
                
                if (targetElement.tagName === 'PRE') {
                    // For JSON content, we want the raw JSON, not the HTML with formatting
                    const dataType = targetId.includes('header') ? 'header' : 
                                    targetId.includes('payload') ? 'payload' : 'signature';
                    
                    if (dataType === 'header' || dataType === 'payload') {
                        const decodedData = currentDecodedToken[dataType];
                        textToCopy = JSON.stringify(decodedData, null, 2);
                    } else {
                        textToCopy = targetElement.textContent;
                    }
                } else {
                    textToCopy = targetElement.textContent;
                }
                
                copyToClipboard(textToCopy, button);
            }
        });
    });

    // Function to copy text to clipboard with appropriate feedback
    function copyToClipboard(text, button) {
        navigator.clipboard.writeText(text)
            .then(() => {
                showCopyFeedback(button, true);
            })
            .catch(err => {
                console.error('Failed to copy text: ', err);
                showCopyFeedback(button, false);
            });
    }

    // Show visual feedback after copy attempt
    function showCopyFeedback(button, success) {
        const originalText = button.innerHTML;
        const originalClass = button.className;
        
        if (success) {
            button.innerHTML = '<i class="fas fa-check"></i> Copied';
            button.classList.add('btn-success');
            button.classList.remove('btn-outline-primary');
        } else {
            button.innerHTML = '<i class="fas fa-times"></i> Failed';
            button.classList.add('btn-danger');
            button.classList.remove('btn-outline-primary');
        }
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.className = originalClass;
        }, 2000);
    }

    // Store the most recently decoded token
    let currentDecodedToken = null;

    // Main function to process and display the token
    function processToken(token) {
        const decoded = decodeJWT(token);
        
        if (decoded.valid) {
            displayDecodedJWT(decoded);
            analyzeToken(decoded);
            visualizeTimeline(decoded);
            saveTokenToLocalStorage(token);
            currentDecodedToken = decoded;
        } else {
            showError(decoded.error || 'Invalid JWT format');
        }
    }

    // Decode JWT token with enhanced error handling
    function decodeJWT(token) {
        try {
            // Clean up the token by removing any leading/trailing spaces
            token = token.trim();
            
            // Split the token
            const parts = token.split('.');
            
            if (parts.length !== 3) {
                throw new Error('Invalid JWT format. Expected 3 parts separated by dots.');
            }
            
            // Validate token parts
            const headerPart = parts[0];
            const payloadPart = parts[1];
            const signaturePart = parts[2];
            
            if (!headerPart || !payloadPart || !signaturePart) {
                throw new Error('Invalid JWT: All parts must be non-empty');
            }
            
            // Base64Url decode function
            const base64UrlDecode = (str) => {
                // Replace URL-safe characters and add padding if necessary
                let padded = str.replace(/-/g, '+').replace(/_/g, '/');
                const padding = padded.length % 4;
                if (padding) {
                    padded += '='.repeat(4 - padding);
                }
                // Decode and convert to UTF-8 string
                try {
                    return JSON.parse(atob(padded));
                } catch (e) {
                    throw new Error(`Failed to parse JWT part: ${e.message}`);
                }
            };
            
            // Decode header and payload
            const header = base64UrlDecode(headerPart);
            const payload = base64UrlDecode(payloadPart);
            
            // Basic validation of decoded data
            if (!header || typeof header !== 'object') {
                throw new Error('Invalid JWT header: Must be a valid JSON object');
            }
            
            if (!payload || typeof payload !== 'object') {
                throw new Error('Invalid JWT payload: Must be a valid JSON object');
            }
            
            // Return decoded parts with additional metadata
            return {
                header,
                payload,
                signature: signaturePart,
                parts: { headerPart, payloadPart, signaturePart },
                valid: true,
                raw: token
            };
        } catch (error) {
            console.error('JWT decode error:', error);
            return {
                error: error.message,
                valid: false,
                raw: token || ''
            };
        }
    }

    // Format JSON with enhanced highlighting and readability
    function formatJSON(obj) {
        if (!obj) return '';
        
        // Use a more structured approach to JSON formatting
        const formattedJson = JSON.stringify(obj, null, 2)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, match => {
                let cls = 'json-number';
                let color = syntaxColors.number;
                
                if (/^"/.test(match)) {
                    if (/:$/.test(match)) {
                        cls = 'json-key';
                        color = syntaxColors.key;
                    } else {
                        cls = 'json-string';
                        color = syntaxColors.string;
                    }
                } else if (/true|false/.test(match)) {
                    cls = 'json-boolean';
                    color = syntaxColors.boolean;
                } else if (/null/.test(match)) {
                    cls = 'json-null';
                    color = syntaxColors.null;
                }
                
                return `<span class="${cls}" style="color: ${color}">${match}</span>`;
            });
            
        return formattedJson;
    }

    // Format the payload with time-related fields enhanced
    function formatPayloadWithTimeConversion(payload) {
        if (!payload) return '';
        
        // Create a copy to avoid modifying the original
        const payloadCopy = JSON.parse(JSON.stringify(payload));
        const timeFields = ['exp', 'iat', 'nbf', 'auth_time'];
        
        // Process the JSON and add comments for time fields
        let formattedJson = JSON.stringify(payloadCopy, null, 2);
        
        // Replace the time values with formatted time strings
        timeFields.forEach(field => {
            if (payloadCopy[field] && typeof payloadCopy[field] === 'number') {
                const date = new Date(payloadCopy[field] * 1000);
                const formattedDate = date.toLocaleString();
                
                // Use regex to find the specific field value in the JSON
                const regex = new RegExp(`"${field}":\\s*(\\d+)`, 'g');
                formattedJson = formattedJson.replace(regex, `"${field}": $1 /* ${formattedDate} */`);
            }
        });
        
        // Apply syntax highlighting
        return formattedJson
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?|\/\*.*?\*\/)/g, match => {
                let cls = 'json-number';
                let color = syntaxColors.number;
                
                if (/^\/\*/.test(match)) {
                    // Comment formatting
                    cls = 'json-comment';
                    color = syntaxColors.comment;
                } else if (/^"/.test(match)) {
                    if (/:$/.test(match)) {
                        cls = 'json-key';
                        color = syntaxColors.key;
                    } else {
                        cls = 'json-string';
                        color = syntaxColors.string;
                    }
                } else if (/true|false/.test(match)) {
                    cls = 'json-boolean';
                    color = syntaxColors.boolean;
                } else if (/null/.test(match)) {
                    cls = 'json-null';
                    color = syntaxColors.null;
                }
                
                return `<span class="${cls}" style="color: ${color}">${match}</span>`;
            });
    }

    // Display the decoded JWT
    function displayDecodedJWT(decoded) {
        // Format header
        headerJson.innerHTML = formatJSON(decoded.header);
        
        // Format payload with time conversion
        payloadJson.innerHTML = formatPayloadWithTimeConversion(decoded.payload);
        
        // Update the signature section
        signatureData.textContent = decoded.signature;
        signatureStatus.innerHTML = `<span class="badge bg-info"><i class="fas fa-info-circle"></i> Signature verification requires the secret key</span>`;
        
        // Update payload claims count
        updatePayloadClaimsCount(decoded.payload);

        // Apply syntax highlighting if highlight.js is available
        if (typeof hljs !== 'undefined') {
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });
        }
        
        // Show the decoded output
        decodedOutput.style.display = 'block';
        
        // Scroll to the output
        decodedOutput.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    // Count and display the number of claims in payload
    function updatePayloadClaimsCount(payload) {
        const count = Object.keys(payload).length;
        payloadClaimsCount.textContent = `${count} claim${count !== 1 ? 's' : ''}`;
    }

    // Analyze token and display information
    function analyzeToken(decoded) {
        // Get algorithm from header
        const alg = decoded.header.alg || 'none';
        tokenAlgorithm.textContent = alg;
        
        // Get timestamps from payload
        const now = Math.floor(Date.now() / 1000);
        const iat = decoded.payload.iat;
        const exp = decoded.payload.exp;
        
        // Format timestamps
        if (iat) {
            const iatDate = new Date(iat * 1000);
            tokenIssuedAt.textContent = `${iatDate.toLocaleString()} (${formatTimeAgo(iat * 1000)})`;
        } else {
            tokenIssuedAt.textContent = 'Not specified';
        }
        
        if (exp) {
            const expDate = new Date(exp * 1000);
            tokenExpiration.textContent = `${expDate.toLocaleString()} (${formatTimeAgo(exp * 1000)})`;
            
            // Check if token is expired
            if (now > exp) {
                tokenStatus.innerHTML = '<span class="badge bg-danger">Expired</span>';
            } else {
                tokenStatus.innerHTML = '<span class="badge bg-success">Valid</span>';
            }
        } else {
            tokenExpiration.textContent = 'Not specified';
            tokenStatus.innerHTML = '<span class="badge bg-warning">No Expiration</span>';
        }
    }

    // Format time ago string
    function formatTimeAgo(timestamp) {
        const now = Date.now();
        const secondsAgo = Math.floor((now - timestamp) / 1000);
        
        if (secondsAgo < 0) {
            return formatTimeRemaining(Math.abs(secondsAgo));
        }
        
        if (secondsAgo < 60) {
            return `${secondsAgo} second${secondsAgo !== 1 ? 's' : ''} ago`;
        }
        
        const minutesAgo = Math.floor(secondsAgo / 60);
        if (minutesAgo < 60) {
            return `${minutesAgo} minute${minutesAgo !== 1 ? 's' : ''} ago`;
        }
        
        const hoursAgo = Math.floor(minutesAgo / 60);
        if (hoursAgo < 24) {
            return `${hoursAgo} hour${hoursAgo !== 1 ? 's' : ''} ago`;
        }
        
        const daysAgo = Math.floor(hoursAgo / 24);
        return `${daysAgo} day${daysAgo !== 1 ? 's' : ''} ago`;
    }

    // Format time remaining string
    function formatTimeRemaining(seconds) {
        if (seconds < 60) {
            return `in ${seconds} second${seconds !== 1 ? 's' : ''}`;
        }
        
        const minutes = Math.floor(seconds / 60);
        if (minutes < 60) {
            return `in ${minutes} minute${minutes !== 1 ? 's' : ''}`;
        }
        
        const hours = Math.floor(minutes / 60);
        if (hours < 24) {
            return `in ${hours} hour${hours !== 1 ? 's' : ''}`;
        }
        
        const days = Math.floor(hours / 24);
        return `in ${days} day${days !== 1 ? 's' : ''}`;
    }

    // Visualize token timeline
    function visualizeTimeline(decoded) {
        if (!tokenTimeline) return;
        
        const payload = decoded.payload;
        const now = Math.floor(Date.now() / 1000);
        
        // Clear previous timeline
        tokenTimeline.innerHTML = '';
        
        // Check if we have the necessary time fields
        if (!payload.iat && !payload.exp && !payload.nbf) {
            tokenTimeline.innerHTML = '<p class="text-muted">No timeline data available (missing iat, exp, nbf claims)</p>';
            return;
        }
        
        // Create a visual timeline
        const timelineHtml = [];
        timelineHtml.push('<div class="token-timeline-container">');
        
        // Timeline line
        timelineHtml.push('<div class="timeline-line"></div>');
        
        // Add points on the timeline
        const timePoints = [];
        
        if (payload.iat) {
            timePoints.push({
                timestamp: payload.iat,
                label: 'Issued',
                icon: 'fa-clock',
                type: 'issued'
            });
        }
        
        if (payload.nbf) {
            timePoints.push({
                timestamp: payload.nbf,
                label: 'Not Before',
                icon: 'fa-hourglass-start',
                type: 'not-before'
            });
        }
        
        // Add "now" marker
        timePoints.push({
            timestamp: now,
            label: 'Now',
            icon: 'fa-circle',
            type: 'now'
        });
        
        if (payload.exp) {
            timePoints.push({
                timestamp: payload.exp,
                label: 'Expires',
                icon: 'fa-hourglass-end',
                type: 'expires'
            });
        }
        
        // Sort time points by timestamp
        timePoints.sort((a, b) => a.timestamp - b.timestamp);
        
        // Convert to percentages for visual representation
        const timeRange = Math.max(
            ...timePoints.map(p => p.timestamp)
        ) - Math.min(...timePoints.map(p => p.timestamp));
        
        // We need at least some range to visualize
        if (timeRange <= 0) {
            // If all points are at the same time, just display them centered
            timePoints.forEach(point => {
                point.position = 50; // Center position (%)
            });
        } else {
            const minTime = Math.min(...timePoints.map(p => p.timestamp));
            timePoints.forEach(point => {
                // Convert to percentage position (10% to 90% range)
                point.position = 10 + ((point.timestamp - minTime) / timeRange) * 80;
            });
        }
        
        // Generate the HTML for each point
        timePoints.forEach(point => {
            let statusClass = '';
            
            // Determine status class based on type and current time
            if (point.type === 'now') {
                statusClass = 'timeline-now';
            } else if (point.type === 'expires') {
                statusClass = now > point.timestamp ? 'timeline-expired' : 'timeline-active';
            } else if (point.type === 'not-before') {
                statusClass = now < point.timestamp ? 'timeline-inactive' : 'timeline-active';
            } else {
                statusClass = 'timeline-active';
            }
            
            const timeString = new Date(point.timestamp * 1000).toLocaleString();
            const timeAgoString = formatTimeAgo(point.timestamp * 1000);
            
            timelineHtml.push(`
                <div class="timeline-point ${statusClass}" style="left: ${point.position}%;">
                    <div class="timeline-icon">
                        <i class="fas ${point.icon}"></i>
                    </div>
                    <div class="timeline-label">${point.label}</div>
                    <div class="timeline-tooltip">
                        ${timeString}<br>${timeAgoString}
                    </div>
                </div>
            `);
        });
        
        timelineHtml.push('</div>');
        
        // Set the timeline HTML
        tokenTimeline.innerHTML = timelineHtml.join('');
        
        // Add simple CSS for the timeline if it doesn't exist
        if (!document.getElementById('timeline-styles')) {
            const style = document.createElement('style');
            style.id = 'timeline-styles';
            style.textContent = `
                .token-timeline-container {
                    position: relative;
                    height: 100px;
                    margin: 20px 0;
                }
                .timeline-line {
                    position: absolute;
                    top: 40px;
                    left: 0;
                    right: 0;
                    height: 2px;
                    background-color: #dee2e6;
                }
                .timeline-point {
                    position: absolute;
                    top: 25px;
                    transform: translateX(-50%);
                    text-align: center;
                }
                .timeline-icon {
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    background-color: #f8f9fa;
                    border: 2px solid #dee2e6;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto;
                    z-index: 2;
                    position: relative;
                }
                .timeline-label {
                    font-size: 12px;
                    margin-top: 5px;
                    font-weight: bold;
                }
                .timeline-tooltip {
                    position: absolute;
                    bottom: 100%;
                    left: 50%;
                    transform: translateX(-50%);
                    background-color: #343a40;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-size: 12px;
                    opacity: 0;
                    visibility: hidden;
                    transition: opacity 0.3s, visibility 0.3s;
                    white-space: nowrap;
                    z-index: 10;
                }
                .timeline-point:hover .timeline-tooltip {
                    opacity: 1;
                    visibility: visible;
                }
                .timeline-active .timeline-icon {
                    background-color: #28a745;
                    border-color: #28a745;
                    color: white;
                }
                .timeline-expired .timeline-icon {
                    background-color: #dc3545;
                    border-color: #dc3545;
                    color: white;
                }
                .timeline-inactive .timeline-icon {
                    background-color: #ffc107;
                    border-color: #ffc107;
                    color: black;
                }
                .timeline-now .timeline-icon {
                    background-color: #007bff;
                    border-color: #007bff;
                    color: white;
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Show error message
    function showError(message) {
        decodedOutput.style.display = 'block';
        headerJson.innerHTML = `<div class="alert alert-danger">${message}</div>`;
        payloadJson.innerHTML = '';
        signatureData.textContent = '';
        signatureStatus.innerHTML = '';
        payloadClaimsCount.textContent = '0 claims';
    }

    // Save token to localStorage for persistence
    function saveTokenToLocalStorage(token) {
        if (typeof localStorage !== 'undefined') {
            try {
                localStorage.setItem('jwt_offline_decoder_last_token', token);
            } catch (e) {
                console.warn('Failed to save token to localStorage:', e);
            }
        }
    }

    // Load saved token from localStorage
    function loadTokenFromLocalStorage() {
        if (typeof localStorage !== 'undefined') {
            try {
                return localStorage.getItem('jwt_offline_decoder_last_token');
            } catch (e) {
                console.warn('Failed to load token from localStorage:', e);
                return null;
            }
        }
        return null;
    }

    // Initialize the decoder
    function init() {
        // 初始化提示工具
        initializeTooltips();
        
        // Check for a token in the URL
        const urlParams = new URLSearchParams(window.location.search);
        const tokenParam = urlParams.get('token');
        
        if (tokenParam) {
            // Set the token from URL parameter
            jwtInput.value = tokenParam;
            processToken(tokenParam);
        } else {
            // Check for a previously saved token
            const savedToken = loadTokenFromLocalStorage();
            
            if (savedToken) {
                jwtInput.value = savedToken;
                // Optional: automatically decode the saved token
                // processToken(savedToken);
            }
        }
        
        // Add CSS for the token timeline if Chart.js is not available
        if (typeof Chart === 'undefined') {
            // Add fallback CSS for visualization
        }
    }
    
    // 初始化提示工具
    function initializeTooltips() {
        // 确保在DOM完全加载后初始化
        setTimeout(() => {
            if (typeof tippy === 'undefined') {
                // 如果Tippy.js未加载，使用原生工具提示
                document.querySelectorAll('[data-tippy-content]').forEach(element => {
                    const content = element.getAttribute('data-tippy-content');
                    if (content) {
                        element.setAttribute('title', content);
                    }
                });
            } else {
                try {
                    // 使用选择器前先检查是否有匹配的元素
                    const elements = document.querySelectorAll('[data-tippy-content]');
                    if (elements.length > 0) {
                        // 单独处理每个元素，避免选择器问题
                        elements.forEach(element => {
                            tippy(element, {
                                content: element.getAttribute('data-tippy-content'),
                                arrow: true,
                                delay: [100, 200],
                                placement: 'top',
                                // 避免使用singleton功能
                                interactive: true,
                                appendTo: () => document.body
                            });
                        });
                    }
                    
                    // 不要使用createSingleton，它可能导致错误
                    // 如果需要统一样式，可以使用defaultProps
                    if (tippy.setDefaultProps) {
                        tippy.setDefaultProps({
                            theme: 'light-border',
                            animation: 'shift-away'
                        });
                    }
                } catch (error) {
                    console.warn('Failed to initialize Tippy.js:', error);
                    // 回退到原生工具提示
                    document.querySelectorAll('[data-tippy-content]').forEach(element => {
                        const content = element.getAttribute('data-tippy-content');
                        if (content) {
                            element.setAttribute('title', content);
                        }
                    });
                }
            }
        }, 200); // 稍微延迟以确保DOM加载完成
    }

    // Initialize when DOM is loaded
    init();
}); 